# Linux网络配置工具集使用指南

## 工具概述

本工具集提供了三个专业的网络配置脚本，满足不同的网络配置需求：

### 1. setup_network_interfaces_enhanced.sh
**综合网络配置工具** - 功能最全面的主工具

**适用场景**：
- 需要同时配置主网卡和子网卡
- 需要IPv4和IPv6混合配置
- 生产环境的网络配置
- 需要配置文件持久化

**主要功能**：
- 支持IPv4和IPv6混合配置
- 支持多种Linux发行版
- 配置文件自动持久化
- 完整的删除和管理功能

### 2. setup_ipv6_subinterface.sh
**IPv6子网卡专用工具** - 专注IPv6配置

**适用场景**：
- 只需要配置IPv6子网卡
- 不想影响主网卡现有配置
- IPv6专用网络环境
- 需要精确控制IPv6参数

**主要功能**：
- 专门的IPv6子网卡配置
- 支持IPv6地址、前缀长度、网关
- 不影响主网卡配置
- 简单易用的参数

### 3. setup_dual_stack_network.sh
**双栈测试工具** - 快速双栈配置

**适用场景**：
- 测试环境快速配置
- IPv4/IPv6双栈验证
- 临时网络配置
- 网络功能测试

**主要功能**：
- 主网卡IPv4 + 子网卡IPv6
- 支持不同的IPv4和IPv6网关
- 即时生效，适合测试
- 详细的连通性测试

## 工具选择指南

### 根据需求选择工具

| 需求场景 | 推荐工具 | 原因 |
|----------|----------|------|
| 生产环境网络配置 | setup_network_interfaces_enhanced.sh | 功能全面，配置持久化 |
| 只配置IPv6子网卡 | setup_ipv6_subinterface.sh | 专业简单，不影响主网卡 |
| 测试双栈网络 | setup_dual_stack_network.sh | 快速配置，即时生效 |
| IPv4主网卡+IPv6子网卡 | setup_network_interfaces_enhanced.sh | 支持混合配置 |
| 临时网络测试 | setup_dual_stack_network.sh | 适合临时配置 |
| 复杂网络环境 | setup_network_interfaces_enhanced.sh | 功能最全面 |

### 根据技术水平选择

| 技术水平 | 推荐工具 | 说明 |
|----------|----------|------|
| 初学者 | setup_ipv6_subinterface.sh | 参数简单，专注单一功能 |
| 中级用户 | setup_dual_stack_network.sh | 功能适中，测试友好 |
| 高级用户 | setup_network_interfaces_enhanced.sh | 功能全面，灵活配置 |

## 使用示例对比

### 场景1：只配置IPv6子网卡

#### 方法1：使用IPv6专用工具（推荐）
```bash
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -G 2001:db8::1
```

#### 方法2：使用综合工具
```bash
./setup_network_interfaces_enhanced.sh -i eth0 -6 2001:db8::2 -G 2001:db8::1
```

**推荐理由**：IPv6专用工具更简单，参数更少，专注IPv6配置。

### 场景2：IPv4主网卡 + IPv6子网卡

#### 方法1：使用综合工具（推荐）
```bash
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -g *********** -6 2001:db8::2 -G 2001:db8::1
```

#### 方法2：使用双栈工具
```bash
./setup_dual_stack_network.sh -i eth0 -4 ************* -g *********** -6 2001:db8::2 -G 2001:db8::1
```

**推荐理由**：综合工具支持配置持久化，适合生产环境。

### 场景3：快速测试双栈网络

#### 推荐：使用双栈测试工具
```bash
./setup_dual_stack_network.sh -i eth0 -4 ************* -6 2001:db8::2
```

**推荐理由**：专为测试设计，配置快速，测试全面。

## 参数对比

### IPv6相关参数

| 参数 | enhanced.sh | ipv6_subinterface.sh | dual_stack.sh |
|------|-------------|---------------------|---------------|
| IPv6地址 | `-6` | `-6` | `-6` |
| IPv6前缀 | `-P` | `-P` | `-p` |
| IPv6网关 | `-G` | `-G` | `-G` |
| 主网卡IPv6 | `-M` | 不支持 | 不支持 |

### IPv4相关参数

| 参数 | enhanced.sh | ipv6_subinterface.sh | dual_stack.sh |
|------|-------------|---------------------|---------------|
| IPv4地址 | `-s` | 不支持 | 不支持 |
| 主网卡IPv4 | `-m` | 不支持 | `-4` |
| IPv4网关 | `-g` | 不支持 | `-g` |
| 子网掩码 | `-n` | 不支持 | `-m` |

## 配置文件支持

### 持久化配置对比

| 工具 | 配置持久化 | 支持系统 |
|------|------------|----------|
| setup_network_interfaces_enhanced.sh | ✅ 完整支持 | Debian/Ubuntu, RHEL/CentOS, SUSE, systemd |
| setup_ipv6_subinterface.sh | ✅ 完整支持 | Debian/Ubuntu, RHEL/CentOS, 通用系统 |
| setup_dual_stack_network.sh | ❌ 临时配置 | 所有系统（重启后失效） |

## 最佳实践

### 1. 生产环境配置

```bash
# 步骤1：使用综合工具配置
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -g *********** -6 2001:db8::2 -G 2001:db8::1

# 步骤2：验证配置
ip addr show eth0
ip route show
ip -6 route show

# 步骤3：测试连通性
ping -c 3 ***********
ping6 -c 3 2001:db8::1
```

### 2. 测试环境配置

```bash
# 步骤1：快速配置
./setup_dual_stack_network.sh -i eth0 -4 ************* -6 2001:db8::2

# 步骤2：自动测试（脚本内置）
# 脚本会自动进行连通性测试

# 步骤3：清理配置（如需要）
ip addr del *************/24 dev eth0
ip -6 addr del 2001:db8::2/64 dev eth0
```

### 3. IPv6专用配置

```bash
# 步骤1：配置IPv6子网卡
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -P 48 -G 2001:db8::1

# 步骤2：验证IPv6配置
ip -6 addr show eth0
ip -6 route show

# 步骤3：测试IPv6连通性
ping6 -c 3 2001:db8::2
ping6 -c 3 2001:db8::1
```

## 故障排除

### 常见问题及解决方案

#### 1. 工具选择错误

**问题**：使用了不合适的工具
**解决方案**：
- 根据需求重新选择合适的工具
- 参考上面的工具选择指南

#### 2. 参数不兼容

**问题**：不同工具的参数不一样
**解决方案**：
- 查看各工具的帮助信息：`./script_name.sh -h`
- 参考参数对比表

#### 3. 配置冲突

**问题**：多个工具配置了相同的接口
**解决方案**：
```bash
# 清理现有配置
ip addr flush dev eth0
ip -6 addr flush dev eth0

# 重新使用合适的工具配置
```

### 调试技巧

#### 1. 查看当前配置
```bash
# 查看所有网络接口
ip addr show

# 查看路由表
ip route show
ip -6 route show

# 查看网络统计
ss -tuln
```

#### 2. 测试网络连通性
```bash
# IPv4测试
ping -c 3 <目标IP>

# IPv6测试
ping6 -c 3 <目标IPv6>

# 指定源地址测试
ping -c 3 -I <源IP> <目标IP>
ping6 -c 3 -I <源IPv6> <目标IPv6>
```

#### 3. 查看配置文件
```bash
# Debian/Ubuntu
ls -la /etc/network/interfaces.d/

# RHEL/CentOS
ls -la /etc/sysconfig/network-scripts/

# 通用
cat /etc/rc.local
```

## 工具升级和维护

### 版本管理
- 定期备份配置文件
- 测试新版本功能
- 保持工具更新

### 配置备份
```bash
# 备份网络配置
cp -r /etc/network/ /etc/network.backup.$(date +%Y%m%d)
cp -r /etc/sysconfig/network-scripts/ /etc/sysconfig/network-scripts.backup.$(date +%Y%m%d)
```

### 日志记录
```bash
# 记录配置操作
echo "$(date): 配置网络接口 eth0" >> /var/log/network-config.log
```

---

**总结**：选择合适的工具是成功配置网络的关键。根据您的具体需求和环境选择最适合的工具，可以大大提高配置效率和成功率。

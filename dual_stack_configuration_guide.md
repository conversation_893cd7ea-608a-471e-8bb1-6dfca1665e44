# IPv4/IPv6双栈配置指南

## 概述

本指南详细说明如何使用网络接口配置工具实现IPv4主网卡和IPv6子网卡的双栈配置，并为它们配置不同的网关。

## 配置场景

**目标配置**：
- 主网卡：IPv4地址 + IPv4网关
- 子网卡：IPv6地址 + IPv6网关
- 两个网卡使用不同的网关进行路由

## 配置步骤

### 1. 基本双栈配置

```bash
# 配置主网卡IPv4和子网卡IPv6
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -g *********** -6 2001:db8::2 -G 2001:db8::1
```

**参数说明**：
- `-i eth0`：指定主网卡名称
- `-m *************`：主网卡IPv4地址
- `-g ***********`：主网卡IPv4网关
- `-6 2001:db8::2`：子网卡IPv6地址
- `-G 2001:db8::1`：子网卡IPv6网关

### 2. 手动配置方法

如果脚本不能完全满足需求，可以使用以下手动配置方法：

#### 步骤1：配置主网卡IPv4
```bash
# 配置主网卡IPv4地址
ip addr add *************/24 dev eth0

# 添加IPv4默认路由
ip route add default via *********** dev eth0
```

#### 步骤2：配置子网卡IPv6
```bash
# 配置子网卡IPv6地址
ip addr add 2001:db8::2/64 dev eth0 label eth0:1

# 添加IPv6路由（针对特定网段）
ip -6 route add 2001:db8::/32 via 2001:db8::1 dev eth0
```

#### 步骤3：配置路由表
```bash
# 查看路由表
ip route show
ip -6 route show

# 添加特定路由（如果需要）
ip route add 10.0.0.0/8 via *********** dev eth0
ip -6 route add 2001:db8:1000::/48 via 2001:db8::1 dev eth0
```

## 配置文件示例

### Debian/Ubuntu系统

#### 主网卡配置文件 (/etc/network/interfaces.d/90-persistent-maininterface.conf)
```
# 主网卡IPv4配置
auto eth0
iface eth0 inet static
    address *************
    netmask *************
    gateway ***********
```

#### 子网卡配置文件 (/etc/network/interfaces.d/91-persistent-subinterface.conf)
```
# 子网卡IPv6配置
auto eth0:1
iface eth0:1 inet6 static
    address 2001:db8::2
    netmask 64
    gateway 2001:db8::1
```

### RHEL/CentOS/Fedora系统

#### 主网卡配置文件 (/etc/sysconfig/network-scripts/ifcfg-eth0)
```
DEVICE=eth0
BOOTPROTO=static
IPADDR=*************
NETMASK=*************
GATEWAY=***********
ONBOOT=yes
```

#### 子网卡配置文件 (/etc/sysconfig/network-scripts/ifcfg-eth0:1)
```
DEVICE=eth0:1
BOOTPROTO=static
IPV6INIT=yes
IPV6ADDR=2001:db8::2/64
IPV6_DEFAULTGW=2001:db8::1
ONBOOT=yes
```

### systemd-networkd系统

#### 网络配置文件 (/etc/systemd/network/10-eth0.network)
```
[Match]
Name=eth0

[Network]
Address=*************/24
Gateway=***********
Address=2001:db8::2/64

[Route]
Gateway=2001:db8::1
Destination=::/0
```

## 路由配置

### IPv4路由配置
```bash
# 添加IPv4默认路由
ip route add default via *********** dev eth0

# 添加特定IPv4路由
ip route add 10.0.0.0/8 via *********** dev eth0
```

### IPv6路由配置
```bash
# 添加IPv6默认路由
ip -6 route add default via 2001:db8::1 dev eth0

# 添加特定IPv6路由
ip -6 route add 2001:db8:1000::/48 via 2001:db8::1 dev eth0
```

## 验证配置

### 检查网络接口
```bash
# 查看所有网络接口
ip addr show

# 查看特定接口
ip addr show eth0
```

### 检查路由表
```bash
# 查看IPv4路由表
ip route show

# 查看IPv6路由表
ip -6 route show
```

### 测试连接
```bash
# 测试IPv4连接
ping -c 3 ***********
ping -c 3 *******

# 测试IPv6连接
ping6 -c 3 2001:db8::1
ping6 -c 3 2001:4860:4860::8888
```

## 故障排除

### 常见问题

1. **IPv6不工作**：
   ```bash
   # 检查IPv6是否启用
   cat /proc/sys/net/ipv6/conf/all/disable_ipv6
   
   # 启用IPv6（如果被禁用）
   echo 0 > /proc/sys/net/ipv6/conf/all/disable_ipv6
   ```

2. **路由冲突**：
   ```bash
   # 查看路由表
   ip route show table all
   ip -6 route show table all
   
   # 删除冲突路由
   ip route del default via ***********
   ip -6 route del default via 2001:db8::1
   ```

3. **网关不可达**：
   ```bash
   # 测试网关连通性
   ping -c 3 ***********
   ping6 -c 3 2001:db8::1
   ```

### 调试命令
```bash
# 查看网络配置
ip addr show
ip route show
ip -6 route show

# 查看网络统计
ss -tuln
netstat -rn

# 查看系统日志
journalctl -u networking
journalctl -u systemd-networkd
```

## 高级配置

### 策略路由
```bash
# 创建路由表
echo "100 ipv4_table" >> /etc/iproute2/rt_tables
echo "101 ipv6_table" >> /etc/iproute2/rt_tables

# 配置策略路由
ip rule add from ************* table ipv4_table
ip route add default via *********** table ipv4_table

ip -6 rule add from 2001:db8::2 table ipv6_table
ip -6 route add default via 2001:db8::1 table ipv6_table
```

### 防火墙配置
```bash
# IPv4防火墙规则
iptables -A INPUT -i eth0 -s ***********/24 -j ACCEPT
iptables -A OUTPUT -o eth0 -d ***********/24 -j ACCEPT

# IPv6防火墙规则
ip6tables -A INPUT -i eth0 -s 2001:db8::/64 -j ACCEPT
ip6tables -A OUTPUT -o eth0 -d 2001:db8::/64 -j ACCEPT
```

## 注意事项

1. **网关兼容性**：确保IPv4和IPv6网关都可达
2. **路由优先级**：IPv4和IPv6路由可能有不同的优先级
3. **DNS配置**：需要配置支持IPv4和IPv6的DNS服务器
4. **应用程序支持**：确保应用程序支持IPv6
5. **防火墙规则**：需要分别配置IPv4和IPv6防火墙规则

#!/bin/bash

# IPv4/IPv6双栈网络配置脚本
# 支持主网卡IPv4和子网卡IPv6配置，使用不同网关

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认值
MAIN_INTERFACE=""
MAIN_IPV4=""
SUB_IPV6=""
IPV4_NETMASK="*************"
IPV6_PREFIX="64"
IPV4_GATEWAY=""
IPV6_GATEWAY=""

# 显示帮助信息
show_help() {
    echo "IPv4/IPv6双栈网络配置工具"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "参数说明:"
    echo "  -i    主网卡名称 (例如: eth0, ens33) - 必填"
    echo "  -4    主网卡IPv4地址 (例如: ***********00) - 必填"
    echo "  -6    子网卡IPv6地址 (例如: 2001:db8::2) - 必填"
    echo "  -m    IPv4子网掩码 (可选，默认: *************)"
    echo "  -p    IPv6前缀长度 (可选，默认: 64)"
    echo "  -g    IPv4网关 (例如: ***********) - 可选"
    echo "  -G    IPv6网关 (例如: 2001:db8::1) - 可选"
    echo "  -h    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 -i eth0 -4 ***********00 -6 2001:db8::2"
    echo "  $0 -i eth0 -4 ***********00 -g *********** -6 2001:db8::2 -G 2001:db8::1"
}

# 检查IPv4地址格式
validate_ipv4() {
    local ip=$1
    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        OIFS=$IFS
        IFS='.'
        ip=($ip)
        IFS=$OIFS
        [[ ${ip[0]} -le 255 && ${ip[1]} -le 255 && ${ip[2]} -le 255 && ${ip[3]} -le 255 ]]
        return $?
    fi
    return 1
}

# 检查IPv6地址格式
validate_ipv6() {
    local ipv6=$1
    if [[ $ipv6 =~ ^([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4}$ ]] || \
       [[ $ipv6 =~ ^::([0-9a-fA-F]{0,4}:){0,6}[0-9a-fA-F]{0,4}$ ]] || \
       [[ $ipv6 =~ ^([0-9a-fA-F]{0,4}:){1,6}:$ ]] || \
       [[ $ipv6 == "::" ]]; then
        return 0
    fi
    return 1
}

# 解析命令行参数
while getopts "i:4:6:m:p:g:G:h" opt; do
    case ${opt} in
        i )
            MAIN_INTERFACE=$OPTARG
            ;;
        4 )
            MAIN_IPV4=$OPTARG
            ;;
        6 )
            SUB_IPV6=$OPTARG
            ;;
        m )
            IPV4_NETMASK=$OPTARG
            ;;
        p )
            IPV6_PREFIX=$OPTARG
            ;;
        g )
            IPV4_GATEWAY=$OPTARG
            ;;
        G )
            IPV6_GATEWAY=$OPTARG
            ;;
        h )
            show_help
            exit 0
            ;;
        \? )
            echo -e "${RED}无效的选项: -$OPTARG${NC}" 1>&2
            show_help
            exit 1
            ;;
        : )
            echo -e "${RED}选项 -$OPTARG 需要参数${NC}" 1>&2
            show_help
            exit 1
            ;;
    esac
done

# 验证必填参数
if [ -z "$MAIN_INTERFACE" ] || [ -z "$MAIN_IPV4" ] || [ -z "$SUB_IPV6" ]; then
    echo -e "${RED}错误: 必须指定主网卡名称(-i)、IPv4地址(-4)和IPv6地址(-6)${NC}"
    show_help
    exit 1
fi

# 验证IP地址格式
if ! validate_ipv4 "$MAIN_IPV4"; then
    echo -e "${RED}错误: IPv4地址格式无效${NC}"
    exit 1
fi

if ! validate_ipv6 "$SUB_IPV6"; then
    echo -e "${RED}错误: IPv6地址格式无效${NC}"
    exit 1
fi

if [ -n "$IPV4_GATEWAY" ] && ! validate_ipv4 "$IPV4_GATEWAY"; then
    echo -e "${RED}错误: IPv4网关地址格式无效${NC}"
    exit 1
fi

if [ -n "$IPV6_GATEWAY" ] && ! validate_ipv6 "$IPV6_GATEWAY"; then
    echo -e "${RED}错误: IPv6网关地址格式无效${NC}"
    exit 1
fi

# 检查主网卡是否存在
if ! ip link show $MAIN_INTERFACE &>/dev/null; then
    echo -e "${RED}错误: 主网卡 $MAIN_INTERFACE 不存在${NC}"
    echo "可用的网卡:"
    ip link | grep -E '^[0-9]+:' | cut -d' ' -f2 | sed 's/://g' | grep -v 'lo'
    exit 1
fi

# 设置子网卡名称
SUB_INTERFACE="${MAIN_INTERFACE}:1"

echo -e "${GREEN}===== 开始配置IPv4/IPv6双栈网络 =====${NC}"
echo -e "${YELLOW}主网卡: $MAIN_INTERFACE${NC}"
echo -e "${YELLOW}主网卡IPv4: $MAIN_IPV4${NC}"
echo -e "${YELLOW}子网卡: $SUB_INTERFACE${NC}"
echo -e "${YELLOW}子网卡IPv6: $SUB_IPV6${NC}"

if [ -n "$IPV4_GATEWAY" ]; then
    echo -e "${YELLOW}IPv4网关: $IPV4_GATEWAY${NC}"
fi

if [ -n "$IPV6_GATEWAY" ]; then
    echo -e "${YELLOW}IPv6网关: $IPV6_GATEWAY${NC}"
fi

echo

# 检查并删除现有配置
echo -e "${YELLOW}清理现有配置...${NC}"

# 删除现有的IP地址
ip addr show $MAIN_INTERFACE | grep "inet $MAIN_IPV4" &>/dev/null && ip addr del $MAIN_IPV4/$IPV4_NETMASK dev $MAIN_INTERFACE 2>/dev/null
ip addr show $MAIN_INTERFACE | grep "inet6 $SUB_IPV6" &>/dev/null && ip addr del $SUB_IPV6/$IPV6_PREFIX dev $MAIN_INTERFACE 2>/dev/null

# 配置主网卡IPv4
echo -e "${YELLOW}配置主网卡IPv4地址...${NC}"
ip addr add $MAIN_IPV4/24 dev $MAIN_INTERFACE

# 配置子网卡IPv6
echo -e "${YELLOW}配置子网卡IPv6地址...${NC}"
ip addr add $SUB_IPV6/$IPV6_PREFIX dev $MAIN_INTERFACE label $SUB_INTERFACE

# 配置IPv4网关
if [ -n "$IPV4_GATEWAY" ]; then
    echo -e "${YELLOW}配置IPv4网关...${NC}"
    # 删除现有的默认路由
    ip route del default 2>/dev/null || true
    # 添加新的默认路由
    ip route add default via $IPV4_GATEWAY dev $MAIN_INTERFACE
fi

# 配置IPv6网关
if [ -n "$IPV6_GATEWAY" ]; then
    echo -e "${YELLOW}配置IPv6网关...${NC}"
    # 删除现有的IPv6默认路由
    ip -6 route del default 2>/dev/null || true
    # 添加新的IPv6默认路由
    ip -6 route add default via $IPV6_GATEWAY dev $MAIN_INTERFACE
fi

echo -e "${GREEN}===== 配置完成 =====${NC}"

# 显示配置结果
echo
echo -e "${YELLOW}当前网络配置:${NC}"
ip addr show $MAIN_INTERFACE

echo
echo -e "${YELLOW}IPv4路由表:${NC}"
ip route show

echo
echo -e "${YELLOW}IPv6路由表:${NC}"
ip -6 route show

# 测试网络连接
echo
echo -e "${YELLOW}正在测试网络连接...${NC}"

# 测试IPv4连接
echo -n "测试IPv4地址 ($MAIN_IPV4): "
if ping -c 1 -W 2 -I $MAIN_IPV4 127.0.0.1 &>/dev/null; then
    echo -e "${GREEN}正常${NC}"
else
    echo -e "${RED}异常${NC}"
fi

# 测试IPv6连接
echo -n "测试IPv6地址 ($SUB_IPV6): "
if ping6 -c 1 -W 2 -I $SUB_IPV6 ::1 &>/dev/null; then
    echo -e "${GREEN}正常${NC}"
else
    echo -e "${RED}异常${NC}"
fi

# 测试IPv4网关
if [ -n "$IPV4_GATEWAY" ]; then
    echo -n "测试IPv4网关 ($IPV4_GATEWAY): "
    if ping -c 1 -W 2 $IPV4_GATEWAY &>/dev/null; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${RED}异常${NC}"
    fi
fi

# 测试IPv6网关
if [ -n "$IPV6_GATEWAY" ]; then
    echo -n "测试IPv6网关 ($IPV6_GATEWAY): "
    if ping6 -c 1 -W 2 $IPV6_GATEWAY &>/dev/null; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${RED}异常${NC}"
    fi
fi

echo
echo -e "${GREEN}双栈网络配置完成！${NC}"
echo -e "${YELLOW}注意: 此配置在系统重启后会丢失，如需永久保存请手动编辑网络配置文件${NC}"
echo
echo -e "${YELLOW}配置摘要:${NC}"
echo -e "  主网卡IPv4: $MAIN_IPV4"
echo -e "  子网卡IPv6: $SUB_IPV6"
[ -n "$IPV4_GATEWAY" ] && echo -e "  IPv4网关: $IPV4_GATEWAY"
[ -n "$IPV6_GATEWAY" ] && echo -e "  IPv6网关: $IPV6_GATEWAY"

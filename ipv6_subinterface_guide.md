# IPv6子网卡配置指南

## 概述

`setup_ipv6_subinterface.sh` 是一个专门用于配置IPv6子网卡的脚本工具。它可以为子网卡单独配置IPv6地址、前缀长度和网关，而不影响主网卡的配置。

## 功能特性

### 1. IPv6子网卡配置
- **IPv6地址配置**：为子网卡配置IPv6地址
- **前缀长度设置**：支持自定义IPv6前缀长度（1-128位）
- **IPv6网关配置**：可选配置IPv6网关
- **独立配置**：不影响主网卡的现有配置

### 2. 配置管理
- **自动验证**：验证IPv6地址格式和前缀长度
- **配置持久化**：根据Linux发行版保存配置文件
- **重启生效**：确保系统重启后配置仍然有效

### 3. 删除功能
- **通过网卡删除**：通过主网卡名称删除IPv6子网卡
- **通过IP删除**：通过IPv6地址查找并删除子网卡
- **安全删除**：只删除IPv6子网卡，不影响主网卡

## 使用方法

### 基本语法

```bash
./setup_ipv6_subinterface.sh [选项]
```

### 参数说明

| 参数 | 说明 | 是否必填 | 示例 |
|------|------|----------|------|
| `-i` | 主网卡名称 | 配置时必填 | `-i eth0` |
| `-6` | IPv6地址 | 配置时必填 | `-6 2001:db8::2` |
| `-P` | IPv6前缀长度 | 可选，默认64 | `-P 48` |
| `-G` | IPv6网关 | 可选 | `-G 2001:db8::1` |
| `-p` | IPv6地址（删除时） | 通过IP删除时必填 | `-p 2001:db8::2` |
| `-d` | 删除子网卡 | 删除操作时必填 | `-d` |
| `-h` | 显示帮助信息 | 可选 | `-h` |

## 配置示例

### 1. 基本IPv6配置

```bash
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2
```

**说明**：
- 在主网卡`eth0`上创建子网卡`eth0:1`
- 配置IPv6地址为`2001:db8::2/64`
- 使用默认前缀长度64位

### 2. 指定前缀长度

```bash
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -P 48
```

**说明**：
- 配置IPv6地址为`2001:db8::2/48`
- 使用48位前缀长度

### 3. 配置IPv6网关

```bash
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -G 2001:db8::1
```

**说明**：
- 配置IPv6地址为`2001:db8::2/64`
- 配置IPv6网关为`2001:db8::1`

### 4. 完整配置

```bash
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -P 48 -G 2001:db8::1
```

**说明**：
- 配置IPv6地址为`2001:db8::2/48`
- 配置IPv6网关为`2001:db8::1`

## 删除示例

### 1. 通过主网卡名称删除

```bash
./setup_ipv6_subinterface.sh -i eth0 -d
```

### 2. 通过IPv6地址删除

```bash
./setup_ipv6_subinterface.sh -p 2001:db8::2 -d
```

## 实际应用场景

### 场景1：IPv6测试环境

**需求**：为测试环境配置IPv6连接
```bash
./setup_ipv6_subinterface.sh -i ens33 -6 2001:db8:test::100 -P 64
```

### 场景2：IPv6服务访问

**需求**：配置IPv6服务访问，使用特定网关
```bash
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8:service::10 -G 2001:db8:service::1
```

### 场景3：IPv6开发环境

**需求**：为开发环境配置IPv6网络
```bash
./setup_ipv6_subinterface.sh -i enp0s3 -6 fd00:dev::50 -P 48 -G fd00:dev::1
```

## 配置文件

### Debian/Ubuntu系统

配置文件：`/etc/network/interfaces.d/91-persistent-ipv6-subinterface.conf`

```
# IPv6子网卡配置 - 用于IPv6连接
auto eth0:1
iface eth0:1 inet6 static
    address 2001:db8::2
    netmask 64
    gateway 2001:db8::1
```

### RHEL/CentOS/Fedora系统

配置文件：`/etc/sysconfig/network-scripts/ifcfg-eth0:1`

```
DEVICE=eth0:1
BOOTPROTO=static
IPV6INIT=yes
IPV6ADDR=2001:db8::2/64
IPV6_DEFAULTGW=2001:db8::1
ONBOOT=yes
```

### 通用系统（rc.local）

配置文件：`/etc/rc.local`

```bash
# 配置IPv6子网卡 eth0:1
ip -6 addr add 2001:db8::2/64 dev eth0
ip -6 route add default via 2001:db8::1 dev eth0 metric 100
```

## 验证配置

### 1. 查看IPv6地址

```bash
# 查看所有IPv6地址
ip -6 addr show

# 查看特定接口的IPv6地址
ip -6 addr show eth0
```

**预期输出**：
```
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 state UP qlen 1000
    inet6 2001:db8::2/64 scope global 
       valid_lft forever preferred_lft forever
    inet6 fe80::xxxx:xxxx:xxxx:xxxx/64 scope link 
       valid_lft forever preferred_lft forever
```

### 2. 查看IPv6路由

```bash
ip -6 route show
```

**预期输出**：
```
default via 2001:db8::1 dev eth0 metric 100
2001:db8::/64 dev eth0 proto kernel metric 256
fe80::/64 dev eth0 proto kernel metric 256
```

### 3. 测试IPv6连通性

```bash
# 测试本地IPv6地址
ping6 -c 3 2001:db8::2

# 测试IPv6网关
ping6 -c 3 2001:db8::1

# 测试外部IPv6地址
ping6 -c 3 2001:4860:4860::8888
```

## 故障排除

### 常见问题

#### 1. IPv6未启用

**问题**：系统禁用了IPv6
**解决方案**：
```bash
# 检查IPv6状态
cat /proc/sys/net/ipv6/conf/all/disable_ipv6

# 启用IPv6
echo 0 | sudo tee /proc/sys/net/ipv6/conf/all/disable_ipv6
echo 0 | sudo tee /proc/sys/net/ipv6/conf/default/disable_ipv6
```

#### 2. IPv6地址格式错误

**问题**：IPv6地址格式不正确
**解决方案**：
- 确保使用正确的IPv6格式
- 支持完整格式：`2001:0db8:0000:0000:0000:0000:0000:0002`
- 支持压缩格式：`2001:db8::2`

#### 3. 网关不可达

**问题**：IPv6网关无法访问
**解决方案**：
```bash
# 检查IPv6邻居表
ip -6 neigh show

# 手动添加邻居
ip -6 neigh add 2001:db8::1 lladdr <MAC地址> dev eth0
```

#### 4. 路由冲突

**问题**：存在多个IPv6默认路由
**解决方案**：
```bash
# 查看所有IPv6路由
ip -6 route show

# 删除冲突路由
ip -6 route del default via <旧网关>
```

### 调试命令

```bash
# 查看IPv6配置
ip -6 addr show
ip -6 route show

# 查看IPv6统计
ss -6 -tuln
netstat -6 -rn

# 查看系统日志
dmesg | grep -i ipv6
journalctl | grep -i ipv6

# 测试IPv6连接
ping6 ::1
ping6 2001:4860:4860::8888
```

## 高级配置

### 1. 多个IPv6地址

```bash
# 为同一子网卡配置多个IPv6地址
ip -6 addr add 2001:db8::2/64 dev eth0
ip -6 addr add 2001:db8::3/64 dev eth0
```

### 2. 临时IPv6地址

```bash
# 配置临时IPv6地址（重启后失效）
ip -6 addr add 2001:db8:temp::1/64 dev eth0
```

### 3. IPv6路由表

```bash
# 查看IPv6路由表
ip -6 route show table all

# 添加特定路由
ip -6 route add 2001:db8:1000::/48 via 2001:db8::1 dev eth0
```

## 注意事项

1. **权限要求**：脚本需要root权限执行
2. **IPv6支持**：确保系统内核支持IPv6
3. **网络设备**：确保网络设备支持IPv6
4. **防火墙**：可能需要配置IPv6防火墙规则
5. **DNS配置**：建议配置支持IPv6的DNS服务器

## 与主脚本的区别

| 特性 | setup_ipv6_subinterface.sh | setup_network_interfaces_enhanced.sh |
|------|---------------------------|--------------------------------------|
| 专注领域 | 专门IPv6子网卡配置 | 综合网络配置（IPv4/IPv6） |
| 配置复杂度 | 简单，专注IPv6 | 复杂，支持多种场景 |
| 参数数量 | 较少，针对性强 | 较多，功能全面 |
| 使用场景 | IPv6专用配置 | 通用网络配置 |

这个专用的IPv6子网卡配置工具为您提供了一个简单而专业的方式来管理IPv6子网卡配置。

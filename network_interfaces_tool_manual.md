# Linux网络接口配置工具使用手册

## 功能概述

这是一个用于配置Linux服务器网络接口的Shell脚本工具，主要功能包括：

1. **配置主网卡和子网卡**：
   - 可以同时配置主网卡和子网卡，也可以只配置子网卡
   - 主网卡用于常规网络连接（可选配置）
   - 子网卡用于直连访问（例如直连笔记本电脑）

2. **删除子网卡**：
   - 支持通过主网卡名称删除子网卡
   - 支持通过子网卡IP地址删除子网卡
   - 安全删除，不会影响主网卡配置

3. **支持多种Linux发行版**：
   - Debian/Ubuntu系统
   - RHEL/CentOS/Fedora系统
   - SUSE系统
   - 使用systemd-networkd的系统
   - 其他Linux系统（通用配置方法）

4. **配置持久化**：
   - 根据不同发行版保存配置到对应的配置文件
   - 确保系统重启后配置仍然有效

5. **网络连接测试**：
   - 配置完成后自动测试网络连接
   - 测试子网卡和主网卡（如果配置了）
   - 测试网关连接（如果有网关）

## 使用方法

### 基本语法

```bash
./setup_network_interfaces_enhanced.sh [选项]
```

### 配置网卡

```bash
./setup_network_interfaces_enhanced.sh -i <主网卡名称> -s <子网卡IP> [-m <主网卡IP>] [-n <子网掩码>]
```

### 删除子网卡

通过主网卡名称删除：
```bash
./setup_network_interfaces_enhanced.sh -i <主网卡名称> -d
```

通过子网卡IP删除：
```bash
./setup_network_interfaces_enhanced.sh -p <子网卡IP> -d
```

### 参数说明

| 参数 | 说明 | 是否必填 | 示例 |
|------|------|----------|------|
| `-i` | 主网卡名称 | 配置时必填 | `-i eth0` |
| `-s` | 子网卡IP地址 | 配置时必填 | `-s *********` |
| `-m` | 主网卡IP地址 | 可选 | `-m *************` |
| `-n` | 子网掩码 | 可选，默认************* | `-n *************` |
| `-p` | 子网卡IP地址 | 通过IP删除子网卡时必填 | `-p *********` |
| `-d` | 删除子网卡 | 删除操作时必填 | `-d` |
| `-h` | 显示帮助信息 | 可选 | `-h` |

## 使用示例

### 1. 只配置子网卡

```bash
./setup_network_interfaces_enhanced.sh -i eth0 -s *********
```

这个命令会在主网卡`eth0`上创建一个子网卡`eth0:1`，并设置IP地址为`*********`，主网卡的配置保持不变。

### 2. 同时配置主网卡和子网卡

```bash
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -s *********
```

这个命令会配置主网卡`eth0`的IP地址为`*************`，并创建子网卡`eth0:1`，设置IP地址为`*********`。

### 3. 通过主网卡名称删除子网卡

```bash
./setup_network_interfaces_enhanced.sh -i eth0 -d
```

这个命令会删除主网卡`eth0`上的子网卡`eth0:1`。

### 4. 通过子网卡IP删除子网卡

```bash
./setup_network_interfaces_enhanced.sh -p ********* -d
```

这个命令会查找IP地址为`*********`的子网卡并删除它。

## 安全特性

1. **保护主网卡配置**：
   - 脚本设计确保不会删除或修改主网卡的IP地址
   - 当检测到IP地址配置在主网卡上而不是子网卡上时，会显示警告并跳过删除操作

2. **配置文件安全**：
   - 在修改配置文件前，会先检查文件是否存在，如果存在则备份
   - 只修改与子网卡相关的配置，不会影响主网卡的配置文件

3. **错误处理**：
   - 提供详细的错误信息和状态反馈
   - 在操作失败时尝试备用方法
   - 添加操作结果的验证步骤

## 支持的系统

脚本会自动检测Linux发行版并使用相应的配置方法：

1. **Debian/Ubuntu**：
   - 配置保存在`/etc/network/interfaces.d/`目录下

2. **RHEL/CentOS/Fedora**：
   - 配置保存在`/etc/sysconfig/network-scripts/`目录下

3. **SUSE**：
   - 配置保存在`/etc/sysconfig/network/`目录下

4. **使用systemd-networkd的系统**：
   - 配置保存在`/etc/systemd/network/`目录下

5. **其他Linux系统**：
   - 使用通用方法配置
   - 配置添加到`/etc/rc.local`以确保重启后生效

## 注意事项

1. 脚本需要以root权限运行
2. 配置前会验证IP地址格式
3. 会检查主网卡是否存在
4. 如果子网卡已存在，会先删除再重新配置
5. 配置完成后会自动测试网络连接
6. 删除子网卡时不会影响主网卡的配置

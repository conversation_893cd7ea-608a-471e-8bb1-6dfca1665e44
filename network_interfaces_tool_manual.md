# Linux网络接口配置工具使用手册（支持IPv4和IPv6）

## 工具概述

本套工具包含多个脚本，用于配置Linux服务器的网络接口，支持IPv4和IPv6双栈配置：

### 1. 主要工具

#### setup_network_interfaces_enhanced.sh
- **综合网络配置工具**：支持IPv4和IPv6混合配置
- **功能全面**：主网卡、子网卡、网关、路由等完整配置
- **多系统支持**：支持多种Linux发行版

#### setup_ipv6_subinterface.sh
- **IPv6专用工具**：专门用于IPv6子网卡配置
- **简单专业**：专注IPv6地址、前缀长度、网关配置
- **独立配置**：不影响主网卡现有配置

#### setup_dual_stack_network.sh
- **双栈配置工具**：专门用于IPv4/IPv6双栈配置
- **不同网关**：支持IPv4和IPv6使用不同网关
- **即时生效**：配置立即生效，适合测试环境

## 功能特性

### 1. 网络接口配置
- **主网卡配置**：IPv4/IPv6地址、网关、子网掩码
- **子网卡配置**：IPv4/IPv6地址、网关、前缀长度
- **混合配置**：支持主网卡IPv4 + 子网卡IPv6等组合
- **独立配置**：可单独配置IPv6子网卡

### 2. 删除和管理
- **安全删除**：通过主网卡名称或IP地址删除子网卡
- **保护主网卡**：删除操作不会影响主网卡配置
- **智能识别**：自动识别IPv4和IPv6地址格式

### 3. 系统兼容性
- **多发行版支持**：Debian/Ubuntu、RHEL/CentOS/Fedora、SUSE
- **配置持久化**：根据系统类型保存相应配置文件
- **重启生效**：确保系统重启后配置仍然有效

3. **支持多种Linux发行版**：
   - Debian/Ubuntu系统
   - RHEL/CentOS/Fedora系统
   - SUSE系统
   - 使用systemd-networkd的系统
   - 其他Linux系统（通用配置方法）

4. **配置持久化**：
   - 根据不同发行版保存配置到对应的配置文件
   - 确保系统重启后配置仍然有效

5. **网络连接测试**：
   - 配置完成后自动测试网络连接
   - 测试子网卡和主网卡（如果配置了）
   - 测试网关连接（如果有网关）

## 使用方法

### 基本语法

```bash
./setup_network_interfaces_enhanced.sh [选项]
```

### 配置IPv4网卡

```bash
./setup_network_interfaces_enhanced.sh -i <主网卡名称> -s <子网卡IP> [-m <主网卡IP>] [-n <子网掩码>]
```

### 配置IPv6网卡

```bash
./setup_network_interfaces_enhanced.sh -i <主网卡名称> -6 <子网卡IPv6> [-M <主网卡IPv6>] [-P <IPv6前缀长度>]
```

### 删除子网卡

通过主网卡名称删除：
```bash
./setup_network_interfaces_enhanced.sh -i <主网卡名称> -d
```

通过子网卡IP删除（IPv4或IPv6）：
```bash
./setup_network_interfaces_enhanced.sh -p <子网卡IP> -d
```

### 参数说明

| 参数 | 说明 | 是否必填 | 示例 |
|------|------|----------|------|
| `-i` | 主网卡名称 | 配置时必填 | `-i eth0` |
| `-s` | 子网卡IPv4地址 | 配置IPv4时必填 | `-s *********` |
| `-m` | 主网卡IPv4地址 | 可选 | `-m *************` |
| `-n` | IPv4子网掩码 | 可选，默认************* | `-n *************` |
| `-6` | 子网卡IPv6地址 | 配置IPv6时必填 | `-6 2001:db8::2` |
| `-M` | 主网卡IPv6地址 | 可选 | `-M 2001:db8::1` |
| `-P` | IPv6前缀长度 | 可选，默认64 | `-P 64` |
| `-p` | 子网卡IP地址（IPv4或IPv6） | 通过IP删除子网卡时必填 | `-p *********` 或 `-p 2001:db8::2` |
| `-d` | 删除子网卡 | 删除操作时必填 | `-d` |
| `-h` | 显示帮助信息 | 可选 | `-h` |

## 工具选择指南

### 根据需求选择合适的工具

| 需求场景 | 推荐工具 | 命令示例 |
|----------|----------|----------|
| 只配置IPv6子网卡 | setup_ipv6_subinterface.sh | `./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2` |
| IPv4/IPv6双栈测试 | setup_dual_stack_network.sh | `./setup_dual_stack_network.sh -i eth0 -4 ************* -6 2001:db8::2` |
| 生产环境综合配置 | setup_network_interfaces_enhanced.sh | `./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -6 2001:db8::2` |

## 使用示例

### setup_network_interfaces_enhanced.sh 示例

#### 1. IPv4配置示例

```bash
# 只配置IPv4子网卡
./setup_network_interfaces_enhanced.sh -i eth0 -s *********

# 同时配置IPv4主网卡和子网卡
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -s *********

# IPv4配置含网关
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -g *********** -s *********
```

#### 2. IPv6配置示例

```bash
# 只配置IPv6子网卡
./setup_network_interfaces_enhanced.sh -i eth0 -6 2001:db8::2

# IPv6子网卡指定前缀长度
./setup_network_interfaces_enhanced.sh -i eth0 -6 2001:db8::2 -P 48

# 同时配置IPv6主网卡和子网卡
./setup_network_interfaces_enhanced.sh -i eth0 -M 2001:db8::1 -6 2001:db8::2
```

#### 3. 双栈配置示例

```bash
# IPv4主网卡 + IPv6子网卡
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -6 2001:db8::2

# 双栈配置 + 不同网关
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -g *********** -6 2001:db8::2 -G 2001:db8::1
```

### setup_ipv6_subinterface.sh 示例

```bash
# 基本IPv6子网卡配置
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2

# 指定前缀长度
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -P 48

# 配置IPv6网关
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -G 2001:db8::1

# 完整IPv6配置
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -P 48 -G 2001:db8::1
```

### setup_dual_stack_network.sh 示例

```bash
# 基本双栈配置
./setup_dual_stack_network.sh -i eth0 -4 ************* -6 2001:db8::2

# 双栈配置含网关
./setup_dual_stack_network.sh -i eth0 -4 ************* -g *********** -6 2001:db8::2 -G 2001:db8::1

# 自定义子网掩码和前缀
./setup_dual_stack_network.sh -i eth0 -4 ********** -m *********** -6 2001:db8::2 -p 48
```

### 删除示例

```bash
# 通过主网卡名称删除（所有工具通用）
./setup_network_interfaces_enhanced.sh -i eth0 -d
./setup_ipv6_subinterface.sh -i eth0 -d

# 通过IP地址删除
./setup_network_interfaces_enhanced.sh -p ********* -d      # IPv4
./setup_network_interfaces_enhanced.sh -p 2001:db8::2 -d    # IPv6
./setup_ipv6_subinterface.sh -p 2001:db8::2 -d             # IPv6专用
```

## 安全特性

1. **保护主网卡配置**：
   - 脚本设计确保不会删除或修改主网卡的IP地址
   - 当检测到IP地址配置在主网卡上而不是子网卡上时，会显示警告并跳过删除操作

2. **配置文件安全**：
   - 在修改配置文件前，会先检查文件是否存在，如果存在则备份
   - 只修改与子网卡相关的配置，不会影响主网卡的配置文件

3. **错误处理**：
   - 提供详细的错误信息和状态反馈
   - 在操作失败时尝试备用方法
   - 添加操作结果的验证步骤

## 支持的系统

脚本会自动检测Linux发行版并使用相应的配置方法：

1. **Debian/Ubuntu**：
   - 配置保存在`/etc/network/interfaces.d/`目录下

2. **RHEL/CentOS/Fedora**：
   - 配置保存在`/etc/sysconfig/network-scripts/`目录下

3. **SUSE**：
   - 配置保存在`/etc/sysconfig/network/`目录下

4. **使用systemd-networkd的系统**：
   - 配置保存在`/etc/systemd/network/`目录下

5. **其他Linux系统**：
   - 使用通用方法配置
   - 配置添加到`/etc/rc.local`以确保重启后生效

## 注意事项

1. 脚本需要以root权限运行
2. 配置前会验证IP地址格式
3. 会检查主网卡是否存在
4. 如果子网卡已存在，会先删除再重新配置
5. 配置完成后会自动测试网络连接
6. 删除子网卡时不会影响主网卡的配置

# IPv6子网卡配置快速参考

## 脚本文件
`setup_ipv6_subinterface.sh` - IPv6子网卡专用配置工具

## 快速命令

### 基本配置
```bash
# 最简配置
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2

# 指定前缀长度
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -P 48

# 配置网关
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -G 2001:db8::1

# 完整配置
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -P 48 -G 2001:db8::1
```

### 删除配置
```bash
# 通过网卡删除
./setup_ipv6_subinterface.sh -i eth0 -d

# 通过IP删除
./setup_ipv6_subinterface.sh -p 2001:db8::2 -d
```

## 参数速查

| 参数 | 说明 | 示例 |
|------|------|------|
| `-i` | 主网卡名称 | `-i eth0` |
| `-6` | IPv6地址 | `-6 2001:db8::2` |
| `-P` | 前缀长度(1-128) | `-P 48` |
| `-G` | IPv6网关 | `-G 2001:db8::1` |
| `-p` | IPv6地址(删除用) | `-p 2001:db8::2` |
| `-d` | 删除标志 | `-d` |
| `-h` | 帮助信息 | `-h` |

## 常用IPv6地址示例

### 文档用地址 (RFC 3849)
```
2001:db8::1
2001:db8::2
2001:db8:1000::1
2001:db8:abcd::1
```

### 本地唯一地址 (ULA)
```
fd00::1
fd12:3456::1
fc00:1234:5678::1
```

### 链路本地地址
```
fe80::1
fe80::2
```

## 验证命令

### 查看配置
```bash
# 查看IPv6地址
ip -6 addr show eth0

# 查看IPv6路由
ip -6 route show

# 查看邻居表
ip -6 neigh show
```

### 测试连接
```bash
# 测试本地
ping6 -c 3 ::1

# 测试配置的地址
ping6 -c 3 2001:db8::2

# 测试网关
ping6 -c 3 2001:db8::1

# 测试外部
ping6 -c 3 2001:4860:4860::8888
```

## 故障排除

### 检查IPv6状态
```bash
# 检查IPv6是否启用
cat /proc/sys/net/ipv6/conf/all/disable_ipv6

# 启用IPv6
echo 0 | sudo tee /proc/sys/net/ipv6/conf/all/disable_ipv6
```

### 手动配置
```bash
# 手动添加IPv6地址
ip -6 addr add 2001:db8::2/64 dev eth0

# 手动添加路由
ip -6 route add default via 2001:db8::1 dev eth0

# 手动删除地址
ip -6 addr del 2001:db8::2/64 dev eth0
```

## 配置文件位置

### Debian/Ubuntu
```
/etc/network/interfaces.d/91-persistent-ipv6-subinterface.conf
```

### RHEL/CentOS/Fedora
```
/etc/sysconfig/network-scripts/ifcfg-eth0:1
```

### 通用系统
```
/etc/rc.local
```

## 常见错误及解决

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| IPv6地址格式无效 | 地址格式错误 | 检查IPv6地址格式 |
| 网卡不存在 | 网卡名称错误 | 使用`ip link show`查看 |
| 权限不足 | 非root用户 | 使用`sudo`执行 |
| IPv6未启用 | 系统禁用IPv6 | 启用IPv6支持 |

## 实用技巧

### 1. 批量配置
```bash
# 为多个网卡配置IPv6
for iface in eth0 eth1 eth2; do
    ./setup_ipv6_subinterface.sh -i $iface -6 2001:db8::$((i+2))
    ((i++))
done
```

### 2. 配置检查
```bash
# 检查配置是否生效
if ip -6 addr show eth0 | grep -q "2001:db8::2"; then
    echo "IPv6配置成功"
else
    echo "IPv6配置失败"
fi
```

### 3. 自动化脚本
```bash
#!/bin/bash
# 自动配置IPv6子网卡
INTERFACE="eth0"
IPV6_BASE="2001:db8::"
GATEWAY="2001:db8::1"

for i in {2..10}; do
    ./setup_ipv6_subinterface.sh -i $INTERFACE -6 ${IPV6_BASE}${i} -G $GATEWAY
done
```

## 网络测试工具

### IPv6连通性测试
```bash
# 基本连通性
ping6 -c 3 2001:db8::1

# 指定源地址
ping6 -c 3 -I 2001:db8::2 2001:db8::1

# 路径MTU发现
ping6 -c 3 -M do -s 1400 2001:db8::1
```

### IPv6服务测试
```bash
# 测试IPv6 HTTP
curl -6 http://[2001:db8::1]/

# 测试IPv6 SSH
ssh user@2001:db8::1

# 测试IPv6 DNS
nslookup google.com 2001:4860:4860::8888
```

## 性能优化

### 1. 路由优化
```bash
# 设置路由优先级
ip -6 route add default via 2001:db8::1 dev eth0 metric 100
```

### 2. 邻居表优化
```bash
# 增加邻居表大小
echo 8192 > /proc/sys/net/ipv6/neigh/default/gc_thresh3
```

### 3. 缓存优化
```bash
# 清理IPv6路由缓存
ip -6 route flush cache
```

---

**提示**: 使用 `./setup_ipv6_subinterface.sh -h` 查看完整帮助信息

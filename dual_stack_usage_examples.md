# IPv4/IPv6双栈配置使用示例

## 概述

本文档提供了使用`setup_dual_stack_network.sh`脚本配置IPv4主网卡和IPv6子网卡的详细示例。

## 脚本特性

- **专门的双栈配置**：专为IPv4/IPv6双栈环境设计
- **不同网关支持**：支持为IPv4和IPv6配置不同的网关
- **自动验证**：自动验证IP地址格式和网络连通性
- **即时生效**：配置立即生效，无需重启
- **详细反馈**：提供详细的配置过程和测试结果

## 基本使用方法

### 1. 最简配置（无网关）

```bash
./setup_dual_stack_network.sh -i eth0 -4 ************* -6 2001:db8::2
```

**说明**：
- 主网卡`eth0`配置IPv4地址`*************`
- 子网卡`eth0:1`配置IPv6地址`2001:db8::2`
- 不配置网关

### 2. 完整配置（包含网关）

```bash
./setup_dual_stack_network.sh -i eth0 -4 ************* -g *********** -6 2001:db8::2 -G 2001:db8::1
```

**说明**：
- 主网卡`eth0`配置IPv4地址`*************`，网关`***********`
- 子网卡`eth0:1`配置IPv6地址`2001:db8::2`，网关`2001:db8::1`

### 3. 自定义子网掩码和前缀长度

```bash
./setup_dual_stack_network.sh -i eth0 -4 ********** -m *********** -6 2001:db8::2 -p 48
```

**说明**：
- IPv4使用`***********`子网掩码（/16）
- IPv6使用`48`位前缀长度

## 实际应用场景

### 场景1：企业内网双栈配置

**需求**：
- 内网IPv4：`*********00/24`，网关：`*********`
- 外网IPv6：`2001:db8:1000::100/64`，网关：`2001:db8:1000::1`

**配置命令**：
```bash
./setup_dual_stack_network.sh \
  -i ens33 \
  -4 *********00 \
  -g ********* \
  -6 2001:db8:1000::100 \
  -G 2001:db8:1000::1
```

### 场景2：服务器双栈配置

**需求**：
- 管理网络IPv4：`**************/24`，网关：`*************`
- 业务网络IPv6：`2001:db8:2000::10/64`，网关：`2001:db8:2000::1`

**配置命令**：
```bash
./setup_dual_stack_network.sh \
  -i eth0 \
  -4 ************** \
  -g ************* \
  -6 2001:db8:2000::10 \
  -G 2001:db8:2000::1
```

### 场景3：测试环境配置

**需求**：
- 本地IPv4：`***********/16`
- 测试IPv6：`fd00::50/64`

**配置命令**：
```bash
./setup_dual_stack_network.sh \
  -i enp0s3 \
  -4 *********** \
  -m *********** \
  -6 fd00::50
```

## 配置验证

### 1. 查看网络接口配置

```bash
# 查看所有接口
ip addr show

# 查看特定接口
ip addr show eth0
```

**预期输出示例**：
```
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP group default qlen 1000
    link/ether 00:0c:29:xx:xx:xx brd ff:ff:ff:ff:ff:ff
    inet *************/24 brd ************* scope global eth0
       valid_lft forever preferred_lft forever
    inet6 2001:db8::2/64 scope global eth0:1
       valid_lft forever preferred_lft forever
```

### 2. 查看路由表

```bash
# IPv4路由表
ip route show

# IPv6路由表
ip -6 route show
```

**预期输出示例**：
```
# IPv4路由
default via *********** dev eth0
***********/24 dev eth0 proto kernel scope link src *************

# IPv6路由
default via 2001:db8::1 dev eth0 metric 1024
2001:db8::/64 dev eth0 proto kernel metric 256
```

### 3. 测试网络连通性

```bash
# 测试IPv4连通性
ping -c 3 ***********
ping -c 3 *******

# 测试IPv6连通性
ping6 -c 3 2001:db8::1
ping6 -c 3 2001:4860:4860::8888
```

## 故障排除

### 常见问题及解决方案

#### 1. IPv6地址无法配置

**问题**：IPv6地址配置失败
**解决方案**：
```bash
# 检查IPv6是否启用
cat /proc/sys/net/ipv6/conf/all/disable_ipv6

# 如果输出为1，则启用IPv6
echo 0 | sudo tee /proc/sys/net/ipv6/conf/all/disable_ipv6
```

#### 2. 网关不可达

**问题**：配置的网关无法ping通
**解决方案**：
```bash
# 检查网关是否在同一网段
ip route get ***********
ip -6 route get 2001:db8::1

# 检查ARP表
arp -a
ip -6 neigh show
```

#### 3. 路由冲突

**问题**：存在多个默认路由
**解决方案**：
```bash
# 查看所有路由表
ip route show table all
ip -6 route show table all

# 删除冲突的路由
ip route del default via <old_gateway>
ip -6 route del default via <old_ipv6_gateway>
```

### 调试命令

```bash
# 网络接口状态
ip link show

# 详细的网络配置
ip addr show

# 路由信息
ip route show
ip -6 route show

# 网络统计
ss -tuln
netstat -rn

# 系统日志
dmesg | grep -i network
journalctl -u networking
```

## 永久配置

脚本配置的网络设置在系统重启后会丢失。要使配置永久生效，需要编辑系统网络配置文件。

### Debian/Ubuntu系统

编辑`/etc/netplan/01-netcfg.yaml`：
```yaml
network:
  version: 2
  ethernets:
    eth0:
      addresses:
        - *************/24
        - 2001:db8::2/64
      gateway4: ***********
      gateway6: 2001:db8::1
      nameservers:
        addresses: [*******, 2001:4860:4860::8888]
```

应用配置：
```bash
sudo netplan apply
```

### RHEL/CentOS系统

编辑`/etc/sysconfig/network-scripts/ifcfg-eth0`：
```
DEVICE=eth0
BOOTPROTO=static
IPADDR=*************
NETMASK=*************
GATEWAY=***********
IPV6INIT=yes
IPV6ADDR=2001:db8::2/64
IPV6_DEFAULTGW=2001:db8::1
ONBOOT=yes
```

重启网络服务：
```bash
sudo systemctl restart network
```

## 注意事项

1. **权限要求**：脚本需要root权限执行
2. **网络中断**：配置过程中可能会短暂中断网络连接
3. **备份配置**：建议在配置前备份现有网络配置
4. **测试环境**：建议先在测试环境中验证配置
5. **IPv6支持**：确保网络设备和ISP支持IPv6

## 扩展功能

### 添加静态路由

```bash
# 添加IPv4静态路由
ip route add 10.0.0.0/8 via ***********

# 添加IPv6静态路由
ip -6 route add 2001:db8:1000::/48 via 2001:db8::1
```

### 配置DNS

```bash
# 编辑DNS配置
echo "nameserver *******" >> /etc/resolv.conf
echo "nameserver 2001:4860:4860::8888" >> /etc/resolv.conf
```

这个双栈配置工具为您提供了一个简单而强大的方式来配置IPv4/IPv6双栈网络环境。

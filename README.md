# Linux网络接口配置工具集

一套专业的Linux网络接口配置工具，支持IPv4和IPv6双栈配置。

## 🚀 快速开始

### 工具概览

| 工具 | 用途 | 适用场景 |
|------|------|----------|
| `setup_network_interfaces_enhanced.sh` | 综合网络配置 | 生产环境，功能全面 |
| `setup_ipv6_subinterface.sh` | IPv6子网卡专用 | IPv6专用配置 |
| `setup_dual_stack_network.sh` | 双栈测试工具 | 测试环境，快速配置 |

### 快速配置示例

```bash
# IPv6子网卡配置（推荐新手）
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -G 2001:db8::1

# 双栈测试配置
./setup_dual_stack_network.sh -i eth0 -4 ************* -6 2001:db8::2

# 生产环境综合配置
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -g *********** -6 2001:db8::2 -G 2001:db8::1
```

## 📋 功能特性

### ✅ 支持的配置
- IPv4主网卡和子网卡配置
- IPv6主网卡和子网卡配置
- IPv4/IPv6双栈混合配置
- 独立的IPv4和IPv6网关
- 自定义子网掩码和前缀长度

### ✅ 支持的系统
- Debian/Ubuntu
- RHEL/CentOS/Fedora
- SUSE/openSUSE
- 其他Linux发行版（通用方法）

### ✅ 安全特性
- 保护主网卡配置不被误删
- IP地址格式自动验证
- 配置前安全检查
- 详细的操作日志

## 📖 文档

### 主要文档
- [**综合使用指南**](network_tools_comprehensive_guide.md) - 工具选择和对比
- [**详细使用手册**](network_interfaces_tool_manual.md) - 完整功能说明
- [**IPv6专用指南**](ipv6_subinterface_guide.md) - IPv6子网卡配置
- [**双栈配置指南**](dual_stack_configuration_guide.md) - IPv4/IPv6双栈配置

### 快速参考
- [**IPv6快速参考**](ipv6_quick_reference.md) - 常用命令速查
- [**双栈使用示例**](dual_stack_usage_examples.md) - 实际应用场景

## 🛠 安装和使用

### 1. 下载工具
```bash
# 确保脚本有执行权限（Linux环境）
chmod +x *.sh
```

### 2. 查看帮助
```bash
# 查看各工具的帮助信息
./setup_network_interfaces_enhanced.sh -h
./setup_ipv6_subinterface.sh -h
./setup_dual_stack_network.sh -h
```

### 3. 选择合适的工具

#### 🎯 我应该使用哪个工具？

**只需要配置IPv6子网卡？**
```bash
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2
```

**需要测试IPv4/IPv6双栈？**
```bash
./setup_dual_stack_network.sh -i eth0 -4 ************* -6 2001:db8::2
```

**生产环境复杂配置？**
```bash
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -6 2001:db8::2
```

## 🔧 常用命令

### IPv6子网卡配置
```bash
# 基本配置
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2

# 完整配置
./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2 -P 48 -G 2001:db8::1

# 删除配置
./setup_ipv6_subinterface.sh -i eth0 -d
```

### 双栈网络配置
```bash
# 快速双栈
./setup_dual_stack_network.sh -i eth0 -4 ************* -6 2001:db8::2

# 双栈含网关
./setup_dual_stack_network.sh -i eth0 -4 ************* -g *********** -6 2001:db8::2 -G 2001:db8::1
```

### 综合网络配置
```bash
# IPv4主网卡 + IPv6子网卡
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -6 2001:db8::2

# 完整双栈配置
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -g *********** -6 2001:db8::2 -G 2001:db8::1
```

## 🔍 验证配置

### 查看网络配置
```bash
# 查看所有网络接口
ip addr show

# 查看IPv4路由
ip route show

# 查看IPv6路由
ip -6 route show
```

### 测试网络连通性
```bash
# 测试IPv4
ping -c 3 ***********

# 测试IPv6
ping6 -c 3 2001:db8::1

# 测试外部连接
ping -c 3 *******
ping6 -c 3 2001:4860:4860::8888
```

## ⚠️ 注意事项

### 权限要求
- 所有脚本需要root权限执行
- 建议使用`sudo`运行脚本

### 系统要求
- Linux内核支持IPv6
- 网络接口存在且可用
- 系统支持ip命令

### 安全提醒
- 配置前建议备份现有网络配置
- 测试环境验证后再用于生产环境
- 远程操作时注意网络中断风险

## 🐛 故障排除

### 常见问题

**IPv6不工作？**
```bash
# 检查IPv6是否启用
cat /proc/sys/net/ipv6/conf/all/disable_ipv6

# 启用IPv6
echo 0 | sudo tee /proc/sys/net/ipv6/conf/all/disable_ipv6
```

**网卡不存在？**
```bash
# 查看可用网卡
ip link show
```

**权限不足？**
```bash
# 使用sudo执行
sudo ./setup_ipv6_subinterface.sh -i eth0 -6 2001:db8::2
```

### 获取帮助
- 查看详细文档：[network_tools_comprehensive_guide.md](network_tools_comprehensive_guide.md)
- 查看脚本帮助：`./script_name.sh -h`
- 查看系统日志：`journalctl -u networking`

## 📝 更新日志

### v2.0 (当前版本)
- ✅ 新增IPv6子网卡专用配置工具
- ✅ 新增IPv4/IPv6双栈测试工具
- ✅ 增强主工具的IPv6支持
- ✅ 完善文档和使用指南
- ✅ 优化用户体验和错误处理

### v1.0
- ✅ 基本的IPv4网络接口配置
- ✅ 支持多种Linux发行版
- ✅ 配置文件持久化

## 📄 许可证

本工具集采用开源许可证，可自由使用和修改。

---

**快速开始**: 选择合适的工具 → 查看帮助信息 → 执行配置命令 → 验证结果

**需要帮助？** 查看 [综合使用指南](network_tools_comprehensive_guide.md) 获取详细说明。

#!/bin/bash

# 比较两个AnyBackupServer目录下的文件差异，使用MD5校验和判断文件是否相同
# 用法: ./compare_directories.sh <第一个AnyBackupServer路径> <第二个AnyBackupServer路径> [排除目录1] [排除目录2] ...
#
# 脚本会输出：
# 1. 两个目录中内容不同的文件（MD5值不同）
# 2. 仅在第一个目录中存在的文件
# 3. 仅在第二个目录中存在的文件
#
# 可以指定多个要排除的目录，这些目录将在比较时被忽略
# 此脚本使用并行处理来提高性能

# 设置最大并行进程数
MAX_PARALLEL=8

# 检查参数数量
if [ $# -lt 2 ]; then
    echo "用法: $0 <第一个AnyBackupServer路径> <第二个AnyBackupServer路径> [排除目录1] [排除目录2] ..."
    exit 1
fi

DIR1="$1"
DIR2="$2"
shift 2  # 移除前两个参数，剩下的都是排除目录

# 使用固定的临时目录，而不是创建后删除
TEMP_DIR="./temp_compare_dirs"
mkdir -p "$TEMP_DIR"
DIFF_FILES="$TEMP_DIR/diff_files.txt"
ONLY_IN_DIR1="$TEMP_DIR/only_in_dir1.txt"
ONLY_IN_DIR2="$TEMP_DIR/only_in_dir2.txt"

# 不删除临时文件，只是覆盖写入
# 清空临时文件（如果存在）
> "$DIFF_FILES"
> "$ONLY_IN_DIR1"
> "$ONLY_IN_DIR2"

# 构建排除目录的参数
EXCLUDE_DIRS=("__pycache__")  # 默认排除__pycache__目录
for exclude_dir in "$@"; do
    EXCLUDE_DIRS+=("$exclude_dir")
done

# 检查目录是否存在
if [ ! -d "$DIR1" ]; then
    echo "错误: 目录 $DIR1 不存在"
    exit 1
fi

if [ ! -d "$DIR2" ]; then
    echo "错误: 目录 $DIR2 不存在"
    exit 1
fi

echo "比较目录 $DIR1 和 $DIR2 中的文件差异 (使用MD5校验)..."
echo "=============================================================="
echo "开始时间: $(date)"

# 显示排除的目录
echo "排除的目录:"
echo "  - __pycache__ (默认排除)"
for exclude_dir in "$@"; do
    echo "  - $exclude_dir"
done

echo "==============================================================="

# 第1步：生成两个目录的文件列表和MD5值
echo "正在生成文件列表和MD5值..."

# 创建文件列表和MD5值的临时文件
FILES1="$TEMP_DIR/files1.txt"
FILES2="$TEMP_DIR/files2.txt"
MD5_1="$TEMP_DIR/md5_1.txt"
MD5_2="$TEMP_DIR/md5_2.txt"

# 清空临时文件（如果存在）
> "$FILES1"
> "$FILES2"
> "$MD5_1"
> "$MD5_2"

# 使用find命令生成文件列表
if [ ${#EXCLUDE_DIRS[@]} -gt 0 ]; then
    # 构建排除目录的find命令
    FIND_CMD1="find \"$DIR1\" -type f"
    FIND_CMD2="find \"$DIR2\" -type f"

    for dir in "${EXCLUDE_DIRS[@]}"; do
        FIND_CMD1="$FIND_CMD1 -not -path \"*/$dir/*\" -not -path \"*/$dir\""
        FIND_CMD2="$FIND_CMD2 -not -path \"*/$dir/*\" -not -path \"*/$dir\""
    done

    eval $FIND_CMD1 > "$FILES1"
    eval $FIND_CMD2 > "$FILES2"
else
    find "$DIR1" -type f > "$FILES1"
    find "$DIR2" -type f > "$FILES2"
fi

# 并行计算第一个目录中所有文件的MD5值
echo "计算第一个目录中文件的MD5值..."
cat "$FILES1" | xargs -P $MAX_PARALLEL -I{} bash -c "md5sum \"{}\" | sed \"s|$DIR1/||\"" > "$MD5_1"

# 并行计算第二个目录中所有文件的MD5值
echo "计算第二个目录中文件的MD5值..."
cat "$FILES2" | xargs -P $MAX_PARALLEL -I{} bash -c "md5sum \"{}\" | sed \"s|$DIR2/||\"" > "$MD5_2"

# 第2步：找出仅在第一个目录中存在的文件
echo "查找仅在第一个目录中存在的文件..."
FILES1_SORTED="$TEMP_DIR/files1_sorted.txt"
FILES2_SORTED="$TEMP_DIR/files2_sorted.txt"
> "$FILES1_SORTED"
> "$FILES2_SORTED"
awk -F'  ' '{print $2}' "$MD5_1" | sort > "$FILES1_SORTED"
awk -F'  ' '{print $2}' "$MD5_2" | sort > "$FILES2_SORTED"
comm -23 "$FILES1_SORTED" "$FILES2_SORTED" > "$ONLY_IN_DIR1"

# 第3步：找出仅在第二个目录中存在的文件
echo "查找仅在第二个目录中存在的文件..."
comm -13 "$TEMP_DIR/files1_sorted.txt" "$TEMP_DIR/files2_sorted.txt" > "$ONLY_IN_DIR2"

# 第4步：找出两个目录中都存在但内容不同的文件
echo "查找内容不同的文件..."
# 创建一个关联数组来存储第一个目录中文件的MD5值
declare -A md5_map1
while IFS='  ' read -r md5 file; do
    md5_map1["$file"]="$md5"
done < "$MD5_1"

# 比较第二个目录中文件的MD5值
while IFS='  ' read -r md5 file; do
    if [ -n "${md5_map1[$file]}" ] && [ "${md5_map1[$file]}" != "$md5" ]; then
        echo "$file|${md5_map1[$file]}|$md5" >> "$DIFF_FILES"
    fi
done < "$MD5_2"

# 第5步：输出结果
echo "=============================================================="
echo "比较结果:"
echo "=============================================================="

# 输出内容不同的文件
if [ -s "$DIFF_FILES" ]; then
    echo "内容不同的文件:"
    while IFS='|' read -r file md5_1 md5_2; do
        echo "  文件不同 (MD5不匹配): $file"
        echo "    $DIR1/$file MD5: $md5_1"
        echo "    $DIR2/$file MD5: $md5_2"
    done < "$DIFF_FILES"
else
    echo "没有找到内容不同的文件"
fi

echo ""

# 输出仅在第一个目录中存在的文件
if [ -s "$ONLY_IN_DIR1" ]; then
    echo "仅在第一个目录中存在的文件:"
    while read -r file; do
        echo "  $file"
    done < "$ONLY_IN_DIR1"
else
    echo "没有找到仅在第一个目录中存在的文件"
fi

echo ""

# 输出仅在第二个目录中存在的文件
if [ -s "$ONLY_IN_DIR2" ]; then
    echo "仅在第二个目录中存在的文件:"
    while read -r file; do
        echo "  $file"
    done < "$ONLY_IN_DIR2"
else
    echo "没有找到仅在第二个目录中存在的文件"
fi

echo "=============================================================="
echo "比较完成"
echo "结束时间: $(date)"
echo "=============================================================="

# IPv6支持功能说明

## 概述

网络接口配置工具现已支持IPv6地址配置，可以为主网卡和子网卡配置IPv6地址。这个功能扩展了原有的IPv4支持，使得工具能够在现代网络环境中更好地工作。

## IPv6功能特性

### 1. IPv6地址验证
- 支持完整格式的IPv6地址（如：2001:0db8:85a3:0000:0000:8a2e:0370:7334）
- 支持压缩格式的IPv6地址（如：2001:db8:85a3::8a2e:370:7334）
- 支持双冒号简写（如：::1, 2001:db8::）
- 自动检测IPv4和IPv6地址格式

### 2. 配置功能
- **主网卡IPv6配置**：可以为主网卡配置IPv6地址
- **子网卡IPv6配置**：可以为子网卡配置IPv6地址
- **前缀长度支持**：支持自定义IPv6前缀长度（默认为64）
- **混合配置**：可以同时配置IPv4和IPv6地址

### 3. 删除功能
- **通过IPv6地址删除**：可以通过IPv6地址查找并删除对应的子网卡
- **智能识别**：自动识别输入的是IPv4还是IPv6地址
- **安全删除**：确保不会删除主网卡上的IPv6地址

## 新增参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `-6` | 子网卡IPv6地址 | `-6 2001:db8::2` |
| `-M` | 主网卡IPv6地址 | `-M 2001:db8::1` |
| `-P` | IPv6前缀长度 | `-P 64` |

## IPv6配置示例

### 基本IPv6配置

```bash
# 只配置子网卡IPv6
./setup_network_interfaces_enhanced.sh -i eth0 -6 2001:db8::2

# 同时配置主网卡和子网卡IPv6
./setup_network_interfaces_enhanced.sh -i eth0 -M 2001:db8::1 -6 2001:db8::2

# 指定IPv6前缀长度
./setup_network_interfaces_enhanced.sh -i eth0 -6 2001:db8::2 -P 48
```

### 混合IPv4和IPv6配置

```bash
# 主网卡IPv4，子网卡IPv6
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -6 2001:db8::2

# 主网卡IPv6，子网卡IPv4
./setup_network_interfaces_enhanced.sh -i eth0 -M 2001:db8::1 -s *********

# 同时配置IPv4和IPv6（需要分别执行）
./setup_network_interfaces_enhanced.sh -i eth0 -m ************* -s *********
./setup_network_interfaces_enhanced.sh -i eth0 -M 2001:db8::1 -6 2001:db8::2
```

### IPv6删除示例

```bash
# 通过IPv6地址删除子网卡
./setup_network_interfaces_enhanced.sh -p 2001:db8::2 -d

# 通过主网卡名称删除所有子网卡（包括IPv6）
./setup_network_interfaces_enhanced.sh -i eth0 -d
```

## 配置文件支持

### Debian/Ubuntu系统
IPv6配置会添加到网络接口配置文件中：
```
# IPv6子网卡配置
auto eth0:1
iface eth0:1 inet6 static
    address 2001:db8::2
    netmask 64
```

### RHEL/CentOS/Fedora系统
IPv6配置会保存到网络脚本文件中：
```
DEVICE=eth0:1
BOOTPROTO=static
IPV6INIT=yes
IPV6ADDR=2001:db8::2/64
ONBOOT=yes
```

### systemd-networkd系统
IPv6配置会添加到网络配置文件中：
```
[Network]
Address=2001:db8::2/64
```

## 注意事项

1. **IPv6地址格式**：
   - 支持标准IPv6地址格式
   - 支持压缩格式（使用::）
   - 不支持IPv4映射的IPv6地址

2. **前缀长度**：
   - 默认前缀长度为64
   - 可以通过-P参数自定义
   - 有效范围：1-128

3. **系统兼容性**：
   - 需要系统内核支持IPv6
   - 某些老版本Linux可能需要额外配置

4. **网络测试**：
   - IPv6网络测试使用ping6命令
   - 需要确保目标支持IPv6连接

## 故障排除

### 常见问题

1. **IPv6地址无法配置**：
   - 检查系统是否启用IPv6支持
   - 确认IPv6模块已加载：`lsmod | grep ipv6`

2. **IPv6地址格式错误**：
   - 确保使用正确的IPv6地址格式
   - 避免使用无效的字符

3. **配置不生效**：
   - 检查网络服务是否正常运行
   - 确认配置文件语法正确

### 调试命令

```bash
# 检查IPv6支持
cat /proc/sys/net/ipv6/conf/all/disable_ipv6

# 查看IPv6地址
ip -6 addr show

# 测试IPv6连接
ping6 ::1
```

## 未来扩展

计划中的IPv6功能扩展：
- IPv6路由配置支持
- IPv6防火墙规则配置
- 更多IPv6网络测试功能

#!/bin/bash

# 配置Linux服务器的主网卡和子网卡
# 主网卡用于常规网络连接(可选)，子网卡用于直连笔记本
# 用法:
#   配置: ./setup_network_interfaces.sh -i <主网卡名称> -s <子网卡IP> [-m <主网卡IP>] [-n <子网掩码>]
#   删除(通过主网卡): ./setup_network_interfaces.sh -i <主网卡名称> -d
#   删除(通过子网卡IP): ./setup_network_interfaces.sh -p <子网卡IP> -d

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 默认值
MAIN_INTERFACE=""
MAIN_IP=""
SUB_IP=""
NETMASK="*************"
CONFIGURE_MAIN=false
DELETE_SUB=false
DELETE_BY_IP=false

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "配置网卡:"
    echo "  $0 -i <主网卡名称> -s <子网卡IP> [-m <主网卡IP>] [-n <子网掩码>]"
    echo
    echo "删除子网卡:"
    echo "  $0 -i <主网卡名称> -d                # 通过主网卡名称删除子网卡"
    echo "  $0 -p <子网卡IP> -d                 # 通过子网卡IP删除子网卡"
    echo
    echo "参数说明:"
    echo "  -i    主网卡名称 (例如: eth0, ens33) - 配置时必填"
    echo "  -s    子网卡IP地址 (例如: *********) - 配置时必填"
    echo "  -m    主网卡IP地址 (例如: *************) - 可选"
    echo "  -n    子网掩码 (可选，默认: *************)"
    echo "  -p    子网卡IP地址 (例如: *********) - 通过IP删除子网卡时必填"
    echo "  -d    删除子网卡 (与 -i 或 -p 一起使用)"
    echo "  -h    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 -i eth0 -s *********                    # 只配置子网卡"
    echo "  $0 -i eth0 -m ************* -s *********   # 同时配置主网卡和子网卡"
    echo "  $0 -i eth0 -d                              # 通过主网卡名称删除子网卡"
    echo "  $0 -p ********* -d                         # 通过子网卡IP删除子网卡"
}

# 检查IP地址格式是否有效
validate_ip() {
    local ip=$1
    local stat=1

    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        OIFS=$IFS
        IFS='.'
        ip=($ip)
        IFS=$OIFS
        [[ ${ip[0]} -le 255 && ${ip[1]} -le 255 && ${ip[2]} -le 255 && ${ip[3]} -le 255 ]]
        stat=$?
    fi
    return $stat
}

# 解析命令行参数
while getopts "i:m:s:n:p:dh" opt; do
    case ${opt} in
        i )
            MAIN_INTERFACE=$OPTARG
            ;;
        m )
            MAIN_IP=$OPTARG
            CONFIGURE_MAIN=true
            ;;
        s )
            SUB_IP=$OPTARG
            ;;
        n )
            NETMASK=$OPTARG
            ;;
        p )
            SUB_IP=$OPTARG
            DELETE_BY_IP=true
            ;;
        d )
            DELETE_SUB=true
            ;;
        h )
            show_help
            exit 0
            ;;
        \? )
            echo -e "${RED}无效的选项: -$OPTARG${NC}" 1>&2
            show_help
            exit 1
            ;;
        : )
            echo -e "${RED}选项 -$OPTARG 需要参数${NC}" 1>&2
            show_help
            exit 1
            ;;
    esac
done

# 检查是否以root权限运行
if [ "$(id -u)" -ne 0 ]; then
    echo -e "${RED}错误: 请以root权限运行此脚本${NC}"
    exit 1
fi

# 验证必填参数
if $DELETE_BY_IP; then
    # 通过IP删除子网卡
    if [ -z "$SUB_IP" ]; then
        echo -e "${RED}错误: 通过IP删除子网卡时必须指定子网卡IP地址 (-p)${NC}"
        show_help
        exit 1
    fi

    # 验证IP地址格式
    if ! validate_ip "$SUB_IP"; then
        echo -e "${RED}错误: 子网卡IP地址格式无效${NC}"
        exit 1
    fi
else
    # 通过主网卡名称操作
    if [ -z "$MAIN_INTERFACE" ]; then
        echo -e "${RED}错误: 必须指定主网卡名称 (-i)${NC}"
        show_help
        exit 1
    fi

    # 检查主网卡是否存在
    if ! ip link show $MAIN_INTERFACE &>/dev/null; then
        echo -e "${RED}错误: 主网卡 $MAIN_INTERFACE 不存在${NC}"
        echo "可用的网卡:"
        ip link | grep -E '^[0-9]+:' | cut -d' ' -f2 | sed 's/://g' | grep -v 'lo'
        exit 1
    fi

    # 设置子网卡名称
    SUB_INTERFACE="${MAIN_INTERFACE}:1"
fi

# 删除子网卡功能
if $DELETE_SUB; then
    echo -e "${GREEN}===== 删除子网卡 =====${NC}"

    # 通过IP删除子网卡
    if $DELETE_BY_IP; then
        echo -e "${YELLOW}正在通过IP地址 $SUB_IP 查找并删除子网卡...${NC}"

        # 查找具有指定IP的网卡
        FOUND_INTERFACES=$(ip addr show | grep -B2 "inet $SUB_IP" | grep -o "^[0-9]\+: [^:]\+" | cut -d' ' -f2)

        if [ -z "$FOUND_INTERFACES" ]; then
            echo -e "${RED}错误: 未找到IP地址为 $SUB_IP 的网卡${NC}"
            exit 1
        fi

        # 遍历找到的网卡
        for INTERFACE in $FOUND_INTERFACES; do
            # 检查是否是子网卡（包含冒号）
            if [[ $INTERFACE == *:* ]]; then
                # 是子网卡，直接获取主网卡名称
                MAIN_INTERFACE=$(echo $INTERFACE | cut -d':' -f1)
                SUB_INTERFACE=$INTERFACE
            else
                # 是主网卡，检查是否有子网卡
                SUB_INTERFACE_FOUND=$(ip addr show | grep "$INTERFACE:" | grep -B2 "inet $SUB_IP" | grep -o "^[0-9]\+: [^:]\+" | cut -d' ' -f2)
                if [ -n "$SUB_INTERFACE_FOUND" ]; then
                    MAIN_INTERFACE=$INTERFACE
                    SUB_INTERFACE=$SUB_INTERFACE_FOUND
                else
                    # 是主网卡但没有子网卡，可能是直接配置在主网卡上的IP
                    MAIN_INTERFACE=$INTERFACE
                    SUB_INTERFACE=$INTERFACE
                fi
            fi

            echo -e "${YELLOW}找到IP地址 $SUB_IP 对应的网卡: $SUB_INTERFACE (主网卡: $MAIN_INTERFACE)${NC}"

            # 获取完整的IP地址和掩码
            SUB_ADDR=$(ip addr show | grep "$SUB_INTERFACE" | grep "inet " | grep "$SUB_IP" | awk '{print $2}')

            if [ -n "$SUB_ADDR" ]; then
                # 删除子网卡IP
                if [[ $SUB_INTERFACE == *:* ]]; then
                    # 是子网卡，使用label参数
                    ip addr del $SUB_ADDR dev $MAIN_INTERFACE label $SUB_INTERFACE 2>/dev/null || true
                    echo -e "${GREEN}子网卡 $SUB_INTERFACE 的IP $SUB_ADDR 已删除${NC}"
                else
                    # 是主网卡上的IP
                    ip addr del $SUB_ADDR dev $MAIN_INTERFACE 2>/dev/null || true
                    echo -e "${GREEN}主网卡 $MAIN_INTERFACE 上的IP $SUB_ADDR 已删除${NC}"
                fi

                # 根据不同发行版删除配置文件
                if [ -f /etc/debian_version ]; then
                    # Debian/Ubuntu
                    if [[ $SUB_INTERFACE == *:* ]] && [ -f "/etc/network/interfaces.d/91-persistent-subinterface.conf" ]; then
                        rm -f "/etc/network/interfaces.d/91-persistent-subinterface.conf"
                        echo -e "${GREEN}已删除配置文件: /etc/network/interfaces.d/91-persistent-subinterface.conf${NC}"
                    fi
                elif [ -f /etc/redhat-release ]; then
                    # RHEL/CentOS/Fedora
                    if [[ $SUB_INTERFACE == *:* ]] && [ -f "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE" ]; then
                        rm -f "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE"
                        echo -e "${GREEN}已删除配置文件: /etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE${NC}"
                    fi
                elif [ -f /etc/SuSE-release ] || [ -f /etc/SUSE-brand ]; then
                    # SUSE
                    if [[ $SUB_INTERFACE == *:* ]] && [ -f "/etc/sysconfig/network/ifcfg-$SUB_INTERFACE" ]; then
                        rm -f "/etc/sysconfig/network/ifcfg-$SUB_INTERFACE"
                        echo -e "${GREEN}已删除配置文件: /etc/sysconfig/network/ifcfg-$SUB_INTERFACE${NC}"
                    fi
                else
                    # systemd-networkd或其他
                    if [ -f "/etc/systemd/network/10-$MAIN_INTERFACE.network" ]; then
                        # 修改systemd-networkd配置，移除子网卡IP
                        sed -i "/Address=$SUB_IP/d" "/etc/systemd/network/10-$MAIN_INTERFACE.network"
                        echo -e "${GREEN}已从配置文件中移除IP地址: /etc/systemd/network/10-$MAIN_INTERFACE.network${NC}"
                        systemctl restart systemd-networkd
                    fi

                    # 修改rc.local
                    if [ -f "/etc/rc.local" ]; then
                        sed -i "/ip addr add.*$SUB_IP/d" /etc/rc.local
                        echo -e "${GREEN}已从/etc/rc.local中移除IP地址配置${NC}"
                    fi
                fi
            else
                echo -e "${YELLOW}未找到网卡 $SUB_INTERFACE 上的IP地址 $SUB_IP${NC}"
            fi
        done
    else
        # 通过主网卡名称删除子网卡
        echo -e "${YELLOW}正在删除子网卡 $SUB_INTERFACE...${NC}"

        # 检查子网卡是否存在
        if ip addr show | grep -q "$SUB_INTERFACE"; then
            # 获取子网卡当前IP和掩码
            SUB_ADDR=$(ip addr show | grep "$SUB_INTERFACE" | grep "inet " | awk '{print $2}')

            if [ -n "$SUB_ADDR" ]; then
                # 删除子网卡IP
                ip addr del $SUB_ADDR dev $MAIN_INTERFACE label $SUB_INTERFACE 2>/dev/null || true
                echo -e "${GREEN}子网卡 $SUB_INTERFACE 已删除${NC}"

                # 根据不同发行版删除配置文件
                if [ -f /etc/debian_version ]; then
                    # Debian/Ubuntu
                    if [ -f "/etc/network/interfaces.d/91-persistent-subinterface.conf" ]; then
                        rm -f "/etc/network/interfaces.d/91-persistent-subinterface.conf"
                        echo -e "${GREEN}已删除配置文件: /etc/network/interfaces.d/91-persistent-subinterface.conf${NC}"
                    fi
                elif [ -f /etc/redhat-release ]; then
                    # RHEL/CentOS/Fedora
                    if [ -f "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE" ]; then
                        rm -f "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE"
                        echo -e "${GREEN}已删除配置文件: /etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE${NC}"
                    fi
                elif [ -f /etc/SuSE-release ] || [ -f /etc/SUSE-brand ]; then
                    # SUSE
                    if [ -f "/etc/sysconfig/network/ifcfg-$SUB_INTERFACE" ]; then
                        rm -f "/etc/sysconfig/network/ifcfg-$SUB_INTERFACE"
                        echo -e "${GREEN}已删除配置文件: /etc/sysconfig/network/ifcfg-$SUB_INTERFACE${NC}"
                    fi
                else
                    # systemd-networkd或其他
                    if [ -f "/etc/systemd/network/10-$MAIN_INTERFACE.network" ]; then
                        # 修改systemd-networkd配置，移除子网卡IP
                        sed -i "/Address=$SUB_ADDR/d" "/etc/systemd/network/10-$MAIN_INTERFACE.network"
                        echo -e "${GREEN}已从配置文件中移除子网卡IP: /etc/systemd/network/10-$MAIN_INTERFACE.network${NC}"
                        systemctl restart systemd-networkd
                    fi

                    # 修改rc.local
                    if [ -f "/etc/rc.local" ]; then
                        sed -i "/ip addr add.*$SUB_INTERFACE/d" /etc/rc.local
                        echo -e "${GREEN}已从/etc/rc.local中移除子网卡配置${NC}"
                    fi
                fi
            else
                echo -e "${YELLOW}未找到子网卡 $SUB_INTERFACE 的IP地址${NC}"
            fi
        else
            echo -e "${YELLOW}子网卡 $SUB_INTERFACE 不存在，无需删除${NC}"
        fi
    fi

    echo -e "${GREEN}子网卡删除操作完成${NC}"
    exit 0
fi

# 配置子网卡功能
if [ -z "$SUB_IP" ]; then
    echo -e "${RED}错误: 配置子网卡时必须指定子网卡IP地址 (-s)${NC}"
    show_help
    exit 1
fi

# 验证IP地址格式
if $CONFIGURE_MAIN; then
    if ! validate_ip "$MAIN_IP"; then
        echo -e "${RED}错误: 主网卡IP地址格式无效${NC}"
        exit 1
    fi
fi

if ! validate_ip "$SUB_IP"; then
    echo -e "${RED}错误: 子网卡IP地址格式无效${NC}"
    exit 1
fi

if ! validate_ip "$NETMASK"; then
    echo -e "${RED}错误: 子网掩码格式无效${NC}"
    exit 1
fi

echo -e "${GREEN}===== 配置Linux网卡以支持直连和网络连接 =====${NC}"
echo

# 获取主网卡当前配置的网关
CURRENT_GATEWAY=$(ip route | grep default | grep $MAIN_INTERFACE | awk '{print $3}')

# 配置网络接口
echo -e "${YELLOW}正在创建子网卡 $SUB_INTERFACE 并设置IP为 $SUB_IP...${NC}"
if $CONFIGURE_MAIN; then
    echo -e "${YELLOW}正在配置主网卡 $MAIN_INTERFACE 的IP为 $MAIN_IP...${NC}"
else
    echo -e "${YELLOW}主网卡 $MAIN_INTERFACE 保持当前配置不变${NC}"
fi

# 检查子网卡是否已存在，如果存在则删除
if ip addr show | grep -q "$SUB_INTERFACE"; then
    echo -e "${YELLOW}子网卡 $SUB_INTERFACE 已存在，将重新配置...${NC}"
    # 获取子网卡当前IP和掩码
    SUB_ADDR=$(ip addr show | grep "$SUB_INTERFACE" | grep "inet " | awk '{print $2}')
    if [ -n "$SUB_ADDR" ]; then
        ip addr del $SUB_ADDR dev $MAIN_INTERFACE 2>/dev/null || true
    fi
fi

# 检测Linux发行版
if [ -f /etc/debian_version ]; then
    # Debian/Ubuntu系统
    echo -e "${YELLOW}检测到Debian/Ubuntu系统${NC}"

    if $CONFIGURE_MAIN; then
        # 配置主网卡
        CONFIG_FILE="/etc/network/interfaces.d/90-persistent-maininterface.conf"
        echo "# 主网卡配置 - 用于常规网络连接" > $CONFIG_FILE
        echo "auto $MAIN_INTERFACE" >> $CONFIG_FILE
        echo "iface $MAIN_INTERFACE inet static" >> $CONFIG_FILE
        echo "    address $MAIN_IP" >> $CONFIG_FILE
        echo "    netmask $NETMASK" >> $CONFIG_FILE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            echo "    gateway $CURRENT_GATEWAY" >> $CONFIG_FILE
        fi
    fi

    # 配置子网卡
    CONFIG_FILE="/etc/network/interfaces.d/91-persistent-subinterface.conf"
    echo "# 子网卡配置 - 用于直连访问" > $CONFIG_FILE
    echo "auto $SUB_INTERFACE" >> $CONFIG_FILE
    echo "iface $SUB_INTERFACE inet static" >> $CONFIG_FILE
    echo "    address $SUB_IP" >> $CONFIG_FILE
    echo "    netmask $NETMASK" >> $CONFIG_FILE

    echo -e "${GREEN}配置已保存到 /etc/network/interfaces.d/${NC}"

    # 立即应用配置
    if $CONFIGURE_MAIN; then
        ip addr flush dev $MAIN_INTERFACE
        ip addr add $MAIN_IP/$NETMASK dev $MAIN_INTERFACE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            ip route add default via $CURRENT_GATEWAY dev $MAIN_INTERFACE
        fi
    fi

    # 添加子网卡
    ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE

elif [ -f /etc/redhat-release ]; then
    # RHEL/CentOS/Fedora系统
    echo -e "${YELLOW}检测到RHEL/CentOS/Fedora系统${NC}"

    if $CONFIGURE_MAIN; then
        # 配置主网卡
        CONFIG_FILE="/etc/sysconfig/network-scripts/ifcfg-$MAIN_INTERFACE"
        echo "DEVICE=$MAIN_INTERFACE" > $CONFIG_FILE
        echo "BOOTPROTO=static" >> $CONFIG_FILE
        echo "IPADDR=$MAIN_IP" >> $CONFIG_FILE
        echo "NETMASK=$NETMASK" >> $CONFIG_FILE
        echo "ONBOOT=yes" >> $CONFIG_FILE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            echo "GATEWAY=$CURRENT_GATEWAY" >> $CONFIG_FILE
        fi
    fi

    # 配置子网卡
    CONFIG_FILE="/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE"
    echo "DEVICE=$SUB_INTERFACE" > $CONFIG_FILE
    echo "BOOTPROTO=static" >> $CONFIG_FILE
    echo "IPADDR=$SUB_IP" >> $CONFIG_FILE
    echo "NETMASK=$NETMASK" >> $CONFIG_FILE
    echo "ONBOOT=yes" >> $CONFIG_FILE

    echo -e "${GREEN}配置已保存到 /etc/sysconfig/network-scripts/${NC}"

    # 立即应用配置
    if $CONFIGURE_MAIN; then
        ip addr flush dev $MAIN_INTERFACE
        ip addr add $MAIN_IP/$NETMASK dev $MAIN_INTERFACE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            ip route add default via $CURRENT_GATEWAY dev $MAIN_INTERFACE
        fi
    fi

    # 添加子网卡
    ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE

elif [ -f /etc/SuSE-release ] || [ -f /etc/SUSE-brand ]; then
    # SUSE系统
    echo -e "${YELLOW}检测到SUSE系统${NC}"

    if $CONFIGURE_MAIN; then
        # 配置主网卡
        CONFIG_FILE="/etc/sysconfig/network/ifcfg-$MAIN_INTERFACE"
        echo "BOOTPROTO=static" > $CONFIG_FILE
        echo "IPADDR=$MAIN_IP" >> $CONFIG_FILE
        echo "NETMASK=$NETMASK" >> $CONFIG_FILE
        echo "STARTMODE=auto" >> $CONFIG_FILE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            echo "GATEWAY=$CURRENT_GATEWAY" >> $CONFIG_FILE
        fi
    fi

    # 配置子网卡
    CONFIG_FILE="/etc/sysconfig/network/ifcfg-$SUB_INTERFACE"
    echo "BOOTPROTO=static" > $CONFIG_FILE
    echo "IPADDR=$SUB_IP" >> $CONFIG_FILE
    echo "NETMASK=$NETMASK" >> $CONFIG_FILE
    echo "STARTMODE=auto" >> $CONFIG_FILE

    echo -e "${GREEN}配置已保存到 /etc/sysconfig/network/${NC}"

    # 立即应用配置
    if $CONFIGURE_MAIN; then
        ip addr flush dev $MAIN_INTERFACE
        ip addr add $MAIN_IP/$NETMASK dev $MAIN_INTERFACE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            ip route add default via $CURRENT_GATEWAY dev $MAIN_INTERFACE
        fi
    fi

    # 添加子网卡
    ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE

else
    # 使用systemd-networkd
    if command -v systemctl &>/dev/null && systemctl is-active systemd-networkd &>/dev/null; then
        echo -e "${YELLOW}检测到systemd-networkd${NC}"

        # 配置主网卡和子网卡
        CONFIG_FILE="/etc/systemd/network/10-$MAIN_INTERFACE.network"

        # 检查文件是否存在，如果存在则备份
        if [ -f "$CONFIG_FILE" ]; then
            cp "$CONFIG_FILE" "${CONFIG_FILE}.bak"
            # 移除现有的子网卡IP配置
            sed -i "/Address=$SUB_IP/d" "$CONFIG_FILE"
        else
            # 创建新文件
            echo "[Match]" > $CONFIG_FILE
            echo "Name=$MAIN_INTERFACE" >> $CONFIG_FILE
            echo "" >> $CONFIG_FILE
            echo "[Network]" >> $CONFIG_FILE
        fi

        # 添加配置
        if $CONFIGURE_MAIN; then
            # 检查是否已有主网卡IP配置
            if ! grep -q "Address=$MAIN_IP" "$CONFIG_FILE"; then
                sed -i "/\[Network\]/a Address=$MAIN_IP/24" "$CONFIG_FILE"
            fi
            if [ ! -z "$CURRENT_GATEWAY" ] && ! grep -q "Gateway=$CURRENT_GATEWAY" "$CONFIG_FILE"; then
                sed -i "/\[Network\]/a Gateway=$CURRENT_GATEWAY" "$CONFIG_FILE"
            fi
        fi

        # 添加子网卡IP
        sed -i "/\[Network\]/a Address=$SUB_IP/24" "$CONFIG_FILE"

        echo -e "${GREEN}配置已保存到 $CONFIG_FILE${NC}"
        systemctl restart systemd-networkd
    else
        echo -e "${YELLOW}无法确定系统类型，使用通用方法配置${NC}"

        # 立即应用配置
        if $CONFIGURE_MAIN; then
            ip addr flush dev $MAIN_INTERFACE
            ip addr add $MAIN_IP/$NETMASK dev $MAIN_INTERFACE
            if [ ! -z "$CURRENT_GATEWAY" ]; then
                ip route add default via $CURRENT_GATEWAY dev $MAIN_INTERFACE
            fi
        fi

        # 添加子网卡
        ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE

        # 创建启动脚本确保重启后配置仍然有效
        RC_LOCAL="/etc/rc.local"

        # 检查rc.local是否存在，如果不存在则创建
        if [ ! -f "$RC_LOCAL" ]; then
            echo '#!/bin/bash' > "$RC_LOCAL"
            echo '' >> "$RC_LOCAL"
            chmod +x "$RC_LOCAL"
        fi

        # 移除旧的网卡配置（如果存在）
        sed -i "/ip addr add.*$SUB_INTERFACE/d" "$RC_LOCAL"

        # 添加子网卡配置到rc.local
        if ! grep -q "exit 0" "$RC_LOCAL"; then
            echo "# 配置子网卡 $SUB_INTERFACE" >> "$RC_LOCAL"
            echo "ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE" >> "$RC_LOCAL"
            echo "" >> "$RC_LOCAL"
            echo "exit 0" >> "$RC_LOCAL"
        else
            # 在exit 0之前添加配置
            sed -i "/exit 0/i # 配置子网卡 $SUB_INTERFACE\nip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE\n" "$RC_LOCAL"
        fi

        echo -e "${GREEN}配置已添加到 $RC_LOCAL 以确保重启后生效${NC}"
    fi
fi

# 显示配置结果
echo
echo -e "${GREEN}===== 配置完成 =====${NC}"
echo -e "${YELLOW}网卡配置信息:${NC}"
ip addr show $MAIN_INTERFACE
echo
echo -e "${GREEN}子网卡 $SUB_INTERFACE 已配置为 $SUB_IP${NC}"
if $CONFIGURE_MAIN; then
    echo -e "${GREEN}主网卡 $MAIN_INTERFACE 已配置为 $MAIN_IP${NC}"
fi

# 测试网络连接
echo
echo -e "${YELLOW}正在测试网络连接...${NC}"

# 测试子网卡
echo -n "测试子网卡 $SUB_INTERFACE ($SUB_IP): "
if ping -c 1 -W 2 -I $SUB_IP 127.0.0.1 &>/dev/null; then
    echo -e "${GREEN}正常${NC}"
else
    echo -e "${RED}异常${NC}"
fi

# 如果配置了主网卡，测试主网卡
if $CONFIGURE_MAIN; then
    echo -n "测试主网卡 $MAIN_INTERFACE ($MAIN_IP): "
    if ping -c 1 -W 2 -I $MAIN_IP 127.0.0.1 &>/dev/null; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${RED}异常${NC}"
    fi

    # 测试网关连接
    if [ ! -z "$CURRENT_GATEWAY" ]; then
        echo -n "测试网关连接 ($CURRENT_GATEWAY): "
        if ping -c 1 -W 2 $CURRENT_GATEWAY &>/dev/null; then
            echo -e "${GREEN}正常${NC}"
        else
            echo -e "${RED}异常${NC}"
        fi
    fi
fi

echo
echo -e "${GREEN}配置已完成。您现在可以通过子网卡IP $SUB_IP 直接连接到此服务器。${NC}"
echo -e "${YELLOW}如需删除子网卡配置，请运行: $0 -i $MAIN_INTERFACE -d${NC}"
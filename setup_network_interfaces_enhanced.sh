#!/bin/bash

# 配置Linux服务器的主网卡和子网卡
# 主网卡用于常规网络连接(可选)，子网卡用于直连笔记本
# 用法: 
#   配置: ./setup_network_interfaces.sh -i <主网卡名称> -s <子网卡IP> [-m <主网卡IP>] [-n <子网掩码>]
#   删除: ./setup_network_interfaces.sh -i <主网卡名称> -d

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 默认值
MAIN_INTERFACE=""
MAIN_IP=""
SUB_IP=""
NETMASK="*************"
CONFIGURE_MAIN=false
DELETE_SUB=false

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "配置网卡:"
    echo "  $0 -i <主网卡名称> -s <子网卡IP> [-m <主网卡IP>] [-n <子网掩码>]"
    echo
    echo "删除子网卡:"
    echo "  $0 -i <主网卡名称> -d"
    echo
    echo "参数说明:"
    echo "  -i    主网卡名称 (例如: eth0, ens33) - 必填"
    echo "  -s    子网卡IP地址 (例如: *********) - 配置时必填"
    echo "  -m    主网卡IP地址 (例如: *************) - 可选"
    echo "  -n    子网掩码 (可选，默认: *************)"
    echo "  -d    删除子网卡 (与 -i 一起使用)"
    echo "  -h    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 -i eth0 -s *********                    # 只配置子网卡"
    echo "  $0 -i eth0 -m ************* -s *********   # 同时配置主网卡和子网卡"
    echo "  $0 -i eth0 -d                              # 删除子网卡"
}

# 检查IP地址格式是否有效
validate_ip() {
    local ip=$1
    local stat=1

    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        OIFS=$IFS
        IFS='.'
        ip=($ip)
        IFS=$OIFS
        [[ ${ip[0]} -le 255 && ${ip[1]} -le 255 && ${ip[2]} -le 255 && ${ip[3]} -le 255 ]]
        stat=$?
    fi
    return $stat
}

# 解析命令行参数
while getopts "i:m:s:n:dh" opt; do
    case ${opt} in
        i )
            MAIN_INTERFACE=$OPTARG
            ;;
        m )
            MAIN_IP=$OPTARG
            CONFIGURE_MAIN=true
            ;;
        s )
            SUB_IP=$OPTARG
            ;;
        n )
            NETMASK=$OPTARG
            ;;
        d )
            DELETE_SUB=true
            ;;
        h )
            show_help
            exit 0
            ;;
        \? )
            echo -e "${RED}无效的选项: -$OPTARG${NC}" 1>&2
            show_help
            exit 1
            ;;
        : )
            echo -e "${RED}选项 -$OPTARG 需要参数${NC}" 1>&2
            show_help
            exit 1
            ;;
    esac
done

# 检查是否以root权限运行
if [ "$(id -u)" -ne 0 ]; then
    echo -e "${RED}错误: 请以root权限运行此脚本${NC}"
    exit 1
fi

# 验证必填参数
if [ -z "$MAIN_INTERFACE" ]; then
    echo -e "${RED}错误: 必须指定主网卡名称 (-i)${NC}"
    show_help
    exit 1
fi

# 检查主网卡是否存在
if ! ip link show $MAIN_INTERFACE &>/dev/null; then
    echo -e "${RED}错误: 主网卡 $MAIN_INTERFACE 不存在${NC}"
    echo "可用的网卡:"
    ip link | grep -E '^[0-9]+:' | cut -d' ' -f2 | sed 's/://g' | grep -v 'lo'
    exit 1
fi

# 设置子网卡名称
SUB_INTERFACE="${MAIN_INTERFACE}:1"

# 删除子网卡功能
if $DELETE_SUB; then
    echo -e "${GREEN}===== 删除子网卡 =====${NC}"
    
    # 检查子网卡是否存在
    if ip addr show | grep -q "$SUB_INTERFACE"; then
        echo -e "${YELLOW}正在删除子网卡 $SUB_INTERFACE...${NC}"
        
        # 获取子网卡当前IP和掩码
        SUB_ADDR=$(ip addr show | grep "$SUB_INTERFACE" | grep "inet " | awk '{print $2}')
        
        if [ -n "$SUB_ADDR" ]; then
            # 删除子网卡IP
            ip addr del $SUB_ADDR dev $MAIN_INTERFACE label $SUB_INTERFACE 2>/dev/null || true
            echo -e "${GREEN}子网卡 $SUB_INTERFACE 已删除${NC}"
            
            # 根据不同发行版删除配置文件
            if [ -f /etc/debian_version ]; then
                # Debian/Ubuntu
                if [ -f "/etc/network/interfaces.d/91-persistent-subinterface.conf" ]; then
                    rm -f "/etc/network/interfaces.d/91-persistent-subinterface.conf"
                    echo -e "${GREEN}已删除配置文件: /etc/network/interfaces.d/91-persistent-subinterface.conf${NC}"
                fi
            elif [ -f /etc/redhat-release ]; then
                # RHEL/CentOS/Fedora
                if [ -f "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE" ]; then
                    rm -f "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE"
                    echo -e "${GREEN}已删除配置文件: /etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE${NC}"
                fi
            elif [ -f /etc/SuSE-release ] || [ -f /etc/SUSE-brand ]; then
                # SUSE
                if [ -f "/etc/sysconfig/network/ifcfg-$SUB_INTERFACE" ]; then
                    rm -f "/etc/sysconfig/network/ifcfg-$SUB_INTERFACE"
                    echo -e "${GREEN}已删除配置文件: /etc/sysconfig/network/ifcfg-$SUB_INTERFACE${NC}"
                fi
            else
                # systemd-networkd或其他
                if [ -f "/etc/systemd/network/10-$MAIN_INTERFACE.network" ]; then
                    # 修改systemd-networkd配置，移除子网卡IP
                    sed -i "/Address=$SUB_ADDR/d" "/etc/systemd/network/10-$MAIN_INTERFACE.network"
                    echo -e "${GREEN}已从配置文件中移除子网卡IP: /etc/systemd/network/10-$MAIN_INTERFACE.network${NC}"
                    systemctl restart systemd-networkd
                fi
                
                # 修改rc.local
                if [ -f "/etc/rc.local" ]; then
                    sed -i "/ip addr add.*$SUB_INTERFACE/d" /etc/rc.local
                    echo -e "${GREEN}已从/etc/rc.local中移除子网卡配置${NC}"
                fi
            fi
        else
            echo -e "${YELLOW}未找到子网卡 $SUB_INTERFACE 的IP地址${NC}"
        fi
    else
        echo -e "${YELLOW}子网卡 $SUB_INTERFACE 不存在，无需删除${NC}"
    fi
    
    echo -e "${GREEN}子网卡删除操作完成${NC}"
    exit 0
fi

# 配置子网卡功能
if [ -z "$SUB_IP" ]; then
    echo -e "${RED}错误: 配置子网卡时必须指定子网卡IP地址 (-s)${NC}"
    show_help
    exit 1
fi

# 验证IP地址格式
if $CONFIGURE_MAIN; then
    if ! validate_ip "$MAIN_IP"; then
        echo -e "${RED}错误: 主网卡IP地址格式无效${NC}"
        exit 1
    fi
fi

if ! validate_ip "$SUB_IP"; then
    echo -e "${RED}错误: 子网卡IP地址格式无效${NC}"
    exit 1
fi

if ! validate_ip "$NETMASK"; then
    echo -e "${RED}错误: 子网掩码格式无效${NC}"
    exit 1
fi

echo -e "${GREEN}===== 配置Linux网卡以支持直连和网络连接 =====${NC}"
echo

# 获取主网卡当前配置的网关
CURRENT_GATEWAY=$(ip route | grep default | grep $MAIN_INTERFACE | awk '{print $3}')

# 配置网络接口
echo -e "${YELLOW}正在创建子网卡 $SUB_INTERFACE 并设置IP为 $SUB_IP...${NC}"
if $CONFIGURE_MAIN; then
    echo -e "${YELLOW}正在配置主网卡 $MAIN_INTERFACE 的IP为 $MAIN_IP...${NC}"
else
    echo -e "${YELLOW}主网卡 $MAIN_INTERFACE 保持当前配置不变${NC}"
fi

# 检查子网卡是否已存在，如果存在则删除
if ip addr show | grep -q "$SUB_INTERFACE"; then
    echo -e "${YELLOW}子网卡 $SUB_INTERFACE 已存在，将重新配置...${NC}"
    # 获取子网卡当前IP和掩码
    SUB_ADDR=$(ip addr show | grep "$SUB_INTERFACE" | grep "inet " | awk '{print $2}')
    if [ -n "$SUB_ADDR" ]; then
        ip addr del $SUB_ADDR dev $MAIN_INTERFACE 2>/dev/null || true
    fi
fi

# 检测Linux发行版
if [ -f /etc/debian_version ]; then
    # Debian/Ubuntu系统
    echo -e "${YELLOW}检测到Debian/Ubuntu系统${NC}"
    
    if $CONFIGURE_MAIN; then
        # 配置主网卡
        CONFIG_FILE="/etc/network/interfaces.d/90-persistent-maininterface.conf"
        echo "# 主网卡配置 - 用于常规网络连接" > $CONFIG_FILE
        echo "auto $MAIN_INTERFACE" >> $CONFIG_FILE
        echo "iface $MAIN_INTERFACE inet static" >> $CONFIG_FILE
        echo "    address $MAIN_IP" >> $CONFIG_FILE
        echo "    netmask $NETMASK" >> $CONFIG_FILE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            echo "    gateway $CURRENT_GATEWAY" >> $CONFIG_FILE
        fi
    fi
    
    # 配置子网卡
    CONFIG_FILE="/etc/network/interfaces.d/91-persistent-subinterface.conf"
    echo "# 子网卡配置 - 用于直连访问" > $CONFIG_FILE
    echo "auto $SUB_INTERFACE" >> $CONFIG_FILE
    echo "iface $SUB_INTERFACE inet static" >> $CONFIG_FILE
    echo "    address $SUB_IP" >> $CONFIG_FILE
    echo "    netmask $NETMASK" >> $CONFIG_FILE
    
    echo -e "${GREEN}配置已保存到 /etc/network/interfaces.d/${NC}"
    
    # 立即应用配置
    if $CONFIGURE_MAIN; then
        ip addr flush dev $MAIN_INTERFACE
        ip addr add $MAIN_IP/$NETMASK dev $MAIN_INTERFACE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            ip route add default via $CURRENT_GATEWAY dev $MAIN_INTERFACE
        fi
    fi
    
    # 添加子网卡
    ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE
    
elif [ -f /etc/redhat-release ]; then
    # RHEL/CentOS/Fedora系统
    echo -e "${YELLOW}检测到RHEL/CentOS/Fedora系统${NC}"
    
    if $CONFIGURE_MAIN; then
        # 配置主网卡
        CONFIG_FILE="/etc/sysconfig/network-scripts/ifcfg-$MAIN_INTERFACE"
        echo "DEVICE=$MAIN_INTERFACE" > $CONFIG_FILE
        echo "BOOTPROTO=static" >> $CONFIG_FILE
        echo "IPADDR=$MAIN_IP" >> $CONFIG_FILE
        echo "NETMASK=$NETMASK" >> $CONFIG_FILE
        echo "ONBOOT=yes" >> $CONFIG_FILE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            echo "GATEWAY=$CURRENT_GATEWAY" >> $CONFIG_FILE
        fi
    fi
    
    # 配置子网卡
    CONFIG_FILE="/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE"
    echo "DEVICE=$SUB_INTERFACE" > $CONFIG_FILE
    echo "BOOTPROTO=static" >> $CONFIG_FILE
    echo "IPADDR=$SUB_IP" >> $CONFIG_FILE
    echo "NETMASK=$NETMASK" >> $CONFIG_FILE
    echo "ONBOOT=yes" >> $CONFIG_FILE
    
    echo -e "${GREEN}配置已保存到 /etc/sysconfig/network-scripts/${NC}"
    
    # 立即应用配置
    if $CONFIGURE_MAIN; then
        ip addr flush dev $MAIN_INTERFACE
        ip addr add $MAIN_IP/$NETMASK dev $MAIN_INTERFACE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            ip route add default via $CURRENT_GATEWAY dev $MAIN_INTERFACE
        fi
    fi
    
    # 添加子网卡
    ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE
    
elif [ -f /etc/SuSE-release ] || [ -f /etc/SUSE-brand ]; then
    # SUSE系统
    echo -e "${YELLOW}检测到SUSE系统${NC}"
    
    if $CONFIGURE_MAIN; then
        # 配置主网卡
        CONFIG_FILE="/etc/sysconfig/network/ifcfg-$MAIN_INTERFACE"
        echo "BOOTPROTO=static" > $CONFIG_FILE
        echo "IPADDR=$MAIN_IP" >> $CONFIG_FILE
        echo "NETMASK=$NETMASK" >> $CONFIG_FILE
        echo "STARTMODE=auto" >> $CONFIG_FILE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            echo "GATEWAY=$CURRENT_GATEWAY" >> $CONFIG_FILE
        fi
    fi
    
    # 配置子网卡
    CONFIG_FILE="/etc/sysconfig/network/ifcfg-$SUB_INTERFACE"
    echo "BOOTPROTO=static" > $CONFIG_FILE
    echo "IPADDR=$SUB_IP" >> $CONFIG_FILE
    echo "NETMASK=$NETMASK" >> $CONFIG_FILE
    echo "STARTMODE=auto" >> $CONFIG_FILE
    
    echo -e "${GREEN}配置已保存到 /etc/sysconfig/network/${NC}"
    
    # 立即应用配置
    if $CONFIGURE_MAIN; then
        ip addr flush dev $MAIN_INTERFACE
        ip addr add $MAIN_IP/$NETMASK dev $MAIN_INTERFACE
        if [ ! -z "$CURRENT_GATEWAY" ]; then
            ip route add default via $CURRENT_GATEWAY dev $MAIN_INTERFACE
        fi
    fi
    
    # 添加子网卡
    ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE
    
else
    # 使用systemd-networkd
    if command -v systemctl &>/dev/null && systemctl is-active systemd-networkd &>/dev/null; then
        echo -e "${YELLOW}检测到systemd-networkd${NC}"
        
        # 配置主网卡和子网卡
        CONFIG_FILE="/etc/systemd/network/10-$MAIN_INTERFACE.network"
        
        # 检查文件是否存在，如果存在则备份
        if [ -f "$CONFIG_FILE" ]; then
            cp "$CONFIG_FILE" "${CONFIG_FILE}.bak"
            # 移除现有的子网卡IP配置
            sed -i "/Address=$SUB_IP/d" "$CONFIG_FILE"
        else
            # 创建新文件
            echo "[Match]" > $CONFIG_FILE
            echo "Name=$MAIN_INTERFACE" >> $CONFIG_FILE
            echo "" >> $CONFIG_FILE
            echo "[Network]" >> $CONFIG_FILE
        fi
        
        # 添加配置
        if $CONFIGURE_MAIN; then
            # 检查是否已有主网卡IP配置
            if ! grep -q "Address=$MAIN_IP" "$CONFIG_FILE"; then
                sed -i "/\[Network\]/a Address=$MAIN_IP/24" "$CONFIG_FILE"
            fi
            if [ ! -z "$CURRENT_GATEWAY" ] && ! grep -q "Gateway=$CURRENT_GATEWAY" "$CONFIG_FILE"; then
                sed -i "/\[Network\]/a Gateway=$CURRENT_GATEWAY" "$CONFIG_FILE"
            fi
        fi
        
        # 添加子网卡IP
        sed -i "/\[Network\]/a Address=$SUB_IP/24" "$CONFIG_FILE"
        
        echo -e "${GREEN}配置已保存到 $CONFIG_FILE${NC}"
        systemctl restart systemd-networkd
    else
        echo -e "${YELLOW}无法确定系统类型，使用通用方法配置${NC}"
        
        # 立即应用配置
        if $CONFIGURE_MAIN; then
            ip addr flush dev $MAIN_INTERFACE
            ip addr add $MAIN_IP/$NETMASK dev $MAIN_INTERFACE
            if [ ! -z "$CURRENT_GATEWAY" ]; then
                ip route add default via $CURRENT_GATEWAY dev $MAIN_INTERFACE
            fi
        fi
        
        # 添加子网卡
        ip addr add $SUB_IP/$NETMASK dev $MAIN_INTERFACE label $SUB_INTERFACE
        
        # 创建启动脚本确保重启后配置仍然有效
        RC_LOCAL="/
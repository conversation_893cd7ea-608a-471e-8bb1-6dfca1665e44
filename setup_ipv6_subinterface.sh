#!/bin/bash

# IPv6子网卡配置脚本
# 专门用于配置子网卡的IPv6地址、网关和前缀长度

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认值
MAIN_INTERFACE=""
SUB_IPV6=""
IPV6_PREFIX="64"
IPV6_GATEWAY=""
DELETE_SUB=false
DELETE_BY_IP=false

# 显示帮助信息
show_help() {
    echo "IPv6子网卡配置工具"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "配置IPv6子网卡:"
    echo "  $0 -i <主网卡名称> -6 <IPv6地址> [-P <前缀长度>] [-G <IPv6网关>]"
    echo
    echo "删除IPv6子网卡:"
    echo "  $0 -i <主网卡名称> -d                # 通过主网卡名称删除"
    echo "  $0 -p <IPv6地址> -d                 # 通过IPv6地址删除"
    echo
    echo "参数说明:"
    echo "  -i    主网卡名称 (例如: eth0, ens33) - 必填"
    echo "  -6    子网卡IPv6地址 (例如: 2001:db8::2) - 配置时必填"
    echo "  -P    IPv6前缀长度 (可选，默认: 64)"
    echo "  -G    IPv6网关 (例如: 2001:db8::1) - 可选"
    echo "  -p    子网卡IPv6地址 - 通过IP删除时必填"
    echo "  -d    删除子网卡"
    echo "  -h    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 -i eth0 -6 2001:db8::2                    # 基本IPv6配置"
    echo "  $0 -i eth0 -6 2001:db8::2 -P 48              # 指定前缀长度"
    echo "  $0 -i eth0 -6 2001:db8::2 -G 2001:db8::1     # 配置IPv6网关"
    echo "  $0 -i eth0 -6 2001:db8::2 -P 48 -G 2001:db8::1  # 完整配置"
    echo "  $0 -i eth0 -d                                # 删除子网卡"
    echo "  $0 -p 2001:db8::2 -d                         # 通过IPv6地址删除"
}

# 检查IPv6地址格式
validate_ipv6() {
    local ipv6=$1
    if [[ $ipv6 =~ ^([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4}$ ]] || \
       [[ $ipv6 =~ ^::([0-9a-fA-F]{0,4}:){0,6}[0-9a-fA-F]{0,4}$ ]] || \
       [[ $ipv6 =~ ^([0-9a-fA-F]{0,4}:){1,6}:$ ]] || \
       [[ $ipv6 == "::" ]]; then
        return 0
    fi
    return 1
}

# 检查IPv6前缀长度
validate_prefix() {
    local prefix=$1
    if [[ $prefix =~ ^[0-9]+$ ]] && [ $prefix -ge 1 ] && [ $prefix -le 128 ]; then
        return 0
    fi
    return 1
}

# 解析命令行参数
while getopts "i:6:P:G:p:dh" opt; do
    case ${opt} in
        i )
            MAIN_INTERFACE=$OPTARG
            ;;
        6 )
            SUB_IPV6=$OPTARG
            ;;
        P )
            IPV6_PREFIX=$OPTARG
            ;;
        G )
            IPV6_GATEWAY=$OPTARG
            ;;
        p )
            SUB_IPV6=$OPTARG
            DELETE_BY_IP=true
            ;;
        d )
            DELETE_SUB=true
            ;;
        h )
            show_help
            exit 0
            ;;
        \? )
            echo -e "${RED}无效的选项: -$OPTARG${NC}" 1>&2
            show_help
            exit 1
            ;;
        : )
            echo -e "${RED}选项 -$OPTARG 需要参数${NC}" 1>&2
            show_help
            exit 1
            ;;
    esac
done

# 验证必填参数
if $DELETE_BY_IP; then
    if [ -z "$SUB_IPV6" ]; then
        echo -e "${RED}错误: 通过IP删除时必须指定IPv6地址 (-p)${NC}"
        show_help
        exit 1
    fi
    
    if ! validate_ipv6 "$SUB_IPV6"; then
        echo -e "${RED}错误: IPv6地址格式无效${NC}"
        exit 1
    fi
else
    if [ -z "$MAIN_INTERFACE" ]; then
        echo -e "${RED}错误: 必须指定主网卡名称 (-i)${NC}"
        show_help
        exit 1
    fi

    # 检查主网卡是否存在
    if ! ip link show $MAIN_INTERFACE &>/dev/null; then
        echo -e "${RED}错误: 主网卡 $MAIN_INTERFACE 不存在${NC}"
        echo "可用的网卡:"
        ip link | grep -E '^[0-9]+:' | cut -d' ' -f2 | sed 's/://g' | grep -v 'lo'
        exit 1
    fi
fi

# 设置子网卡名称
SUB_INTERFACE="${MAIN_INTERFACE}:1"

# 删除IPv6子网卡功能
if $DELETE_SUB; then
    echo -e "${GREEN}===== 删除IPv6子网卡 =====${NC}"
    
    if $DELETE_BY_IP; then
        echo -e "${YELLOW}正在通过IPv6地址 $SUB_IPV6 查找并删除子网卡...${NC}"
        
        # 查找具有指定IPv6地址的网卡
        FOUND_INTERFACES=$(ip -6 addr show | grep -B2 "inet6 $SUB_IPV6" | grep -o "^[0-9]\+: [^:]\+" | cut -d' ' -f2)
        
        if [ -z "$FOUND_INTERFACES" ]; then
            echo -e "${RED}错误: 未找到IPv6地址为 $SUB_IPV6 的网卡${NC}"
            exit 1
        fi
        
        for INTERFACE in $FOUND_INTERFACES; do
            # 查找子网卡
            SUB_IFACES=$(ip -6 addr show $INTERFACE | grep -o "$INTERFACE:[0-9]\+")
            
            for SUB_IFACE in $SUB_IFACES; do
                if ip -6 addr show | grep "$SUB_IFACE" | grep -q "inet6 $SUB_IPV6"; then
                    MAIN_INTERFACE=$(echo $SUB_IFACE | cut -d':' -f1)
                    SUB_INTERFACE=$SUB_IFACE
                    break 2
                fi
            done
        done
        
        if [ -z "$SUB_INTERFACE" ]; then
            echo -e "${RED}错误: 未找到对应的IPv6子网卡${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}正在删除IPv6子网卡 $SUB_INTERFACE...${NC}"
    fi
    
    # 获取IPv6地址和前缀
    IPV6_ADDR=$(ip -6 addr show | grep "$SUB_INTERFACE" | grep "inet6 " | awk '{print $2}' | head -1)
    
    if [ -n "$IPV6_ADDR" ]; then
        # 删除IPv6地址
        ip -6 addr del $IPV6_ADDR dev $MAIN_INTERFACE 2>/dev/null || true
        echo -e "${GREEN}IPv6子网卡 $SUB_INTERFACE 的地址 $IPV6_ADDR 已删除${NC}"
        
        # 删除相关的IPv6路由
        IPV6_ONLY=$(echo $IPV6_ADDR | cut -d'/' -f1)
        ip -6 route del default via $IPV6_ONLY 2>/dev/null || true
        
        # 删除配置文件
        if [ -f /etc/debian_version ]; then
            # Debian/Ubuntu
            if [ -f "/etc/network/interfaces.d/91-persistent-ipv6-subinterface.conf" ]; then
                if grep -q "$IPV6_ONLY" "/etc/network/interfaces.d/91-persistent-ipv6-subinterface.conf"; then
                    rm -f "/etc/network/interfaces.d/91-persistent-ipv6-subinterface.conf"
                    echo -e "${GREEN}已删除配置文件: /etc/network/interfaces.d/91-persistent-ipv6-subinterface.conf${NC}"
                fi
            fi
        elif [ -f /etc/redhat-release ]; then
            # RHEL/CentOS/Fedora
            if [ -f "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE" ]; then
                if grep -q "$IPV6_ONLY" "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE"; then
                    rm -f "/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE"
                    echo -e "${GREEN}已删除配置文件: /etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE${NC}"
                fi
            fi
        fi
        
        # 删除rc.local中的配置
        if [ -f "/etc/rc.local" ]; then
            if grep -q "$IPV6_ONLY" "/etc/rc.local"; then
                sed -i "/ip -6 addr add.*$IPV6_ONLY/d" /etc/rc.local
                sed -i "/ip -6 route add.*$IPV6_ONLY/d" /etc/rc.local
                echo -e "${GREEN}已从/etc/rc.local中移除IPv6配置${NC}"
            fi
        fi
    else
        echo -e "${YELLOW}未找到IPv6子网卡 $SUB_INTERFACE 的地址${NC}"
    fi
    
    echo -e "${GREEN}IPv6子网卡删除操作完成${NC}"
    exit 0
fi

# 配置IPv6子网卡功能
if [ -z "$SUB_IPV6" ]; then
    echo -e "${RED}错误: 配置IPv6子网卡时必须指定IPv6地址 (-6)${NC}"
    show_help
    exit 1
fi

# 验证IPv6地址格式
if ! validate_ipv6 "$SUB_IPV6"; then
    echo -e "${RED}错误: IPv6地址格式无效${NC}"
    exit 1
fi

# 验证前缀长度
if ! validate_prefix "$IPV6_PREFIX"; then
    echo -e "${RED}错误: IPv6前缀长度无效 (1-128)${NC}"
    exit 1
fi

# 验证IPv6网关格式
if [ -n "$IPV6_GATEWAY" ] && ! validate_ipv6 "$IPV6_GATEWAY"; then
    echo -e "${RED}错误: IPv6网关地址格式无效${NC}"
    exit 1
fi

echo -e "${GREEN}===== 配置IPv6子网卡 =====${NC}"
echo -e "${YELLOW}主网卡: $MAIN_INTERFACE${NC}"
echo -e "${YELLOW}子网卡: $SUB_INTERFACE${NC}"
echo -e "${YELLOW}IPv6地址: $SUB_IPV6${NC}"
echo -e "${YELLOW}前缀长度: $IPV6_PREFIX${NC}"
if [ -n "$IPV6_GATEWAY" ]; then
    echo -e "${YELLOW}IPv6网关: $IPV6_GATEWAY${NC}"
fi
echo

# 检查并删除现有的IPv6子网卡配置
if ip -6 addr show | grep -q "$SUB_INTERFACE"; then
    echo -e "${YELLOW}IPv6子网卡 $SUB_INTERFACE 已存在，将重新配置...${NC}"
    EXISTING_IPV6=$(ip -6 addr show | grep "$SUB_INTERFACE" | grep "inet6 " | awk '{print $2}' | head -1)
    if [ -n "$EXISTING_IPV6" ]; then
        ip -6 addr del $EXISTING_IPV6 dev $MAIN_INTERFACE 2>/dev/null || true
    fi
fi

# 配置IPv6地址
echo -e "${YELLOW}正在配置IPv6地址...${NC}"
ip -6 addr add $SUB_IPV6/$IPV6_PREFIX dev $MAIN_INTERFACE

# 配置IPv6网关
if [ -n "$IPV6_GATEWAY" ]; then
    echo -e "${YELLOW}正在配置IPv6网关...${NC}"
    # 添加IPv6路由
    ip -6 route add default via $IPV6_GATEWAY dev $MAIN_INTERFACE metric 100 2>/dev/null || \
    ip -6 route replace default via $IPV6_GATEWAY dev $MAIN_INTERFACE metric 100
fi

# 保存配置到文件
echo -e "${YELLOW}正在保存配置...${NC}"

if [ -f /etc/debian_version ]; then
    # Debian/Ubuntu系统
    CONFIG_FILE="/etc/network/interfaces.d/91-persistent-ipv6-subinterface.conf"
    echo "# IPv6子网卡配置 - 用于IPv6连接" > $CONFIG_FILE
    echo "auto $SUB_INTERFACE" >> $CONFIG_FILE
    echo "iface $SUB_INTERFACE inet6 static" >> $CONFIG_FILE
    echo "    address $SUB_IPV6" >> $CONFIG_FILE
    echo "    netmask $IPV6_PREFIX" >> $CONFIG_FILE
    if [ -n "$IPV6_GATEWAY" ]; then
        echo "    gateway $IPV6_GATEWAY" >> $CONFIG_FILE
    fi
    echo -e "${GREEN}配置已保存到 $CONFIG_FILE${NC}"

elif [ -f /etc/redhat-release ]; then
    # RHEL/CentOS/Fedora系统
    CONFIG_FILE="/etc/sysconfig/network-scripts/ifcfg-$SUB_INTERFACE"
    echo "DEVICE=$SUB_INTERFACE" > $CONFIG_FILE
    echo "BOOTPROTO=static" >> $CONFIG_FILE
    echo "IPV6INIT=yes" >> $CONFIG_FILE
    echo "IPV6ADDR=$SUB_IPV6/$IPV6_PREFIX" >> $CONFIG_FILE
    echo "ONBOOT=yes" >> $CONFIG_FILE
    if [ -n "$IPV6_GATEWAY" ]; then
        echo "IPV6_DEFAULTGW=$IPV6_GATEWAY" >> $CONFIG_FILE
    fi
    echo -e "${GREEN}配置已保存到 $CONFIG_FILE${NC}"

else
    # 通用方法 - 使用rc.local
    RC_LOCAL="/etc/rc.local"
    
    if [ ! -f "$RC_LOCAL" ]; then
        echo '#!/bin/bash' > "$RC_LOCAL"
        echo '' >> "$RC_LOCAL"
        chmod +x "$RC_LOCAL"
    fi
    
    # 移除旧的IPv6配置
    sed -i "/ip -6 addr add.*$SUB_IPV6/d" "$RC_LOCAL"
    sed -i "/ip -6 route add.*$IPV6_GATEWAY/d" "$RC_LOCAL"
    
    # 添加新的IPv6配置
    if ! grep -q "exit 0" "$RC_LOCAL"; then
        echo "# 配置IPv6子网卡 $SUB_INTERFACE" >> "$RC_LOCAL"
        echo "ip -6 addr add $SUB_IPV6/$IPV6_PREFIX dev $MAIN_INTERFACE" >> "$RC_LOCAL"
        if [ -n "$IPV6_GATEWAY" ]; then
            echo "ip -6 route add default via $IPV6_GATEWAY dev $MAIN_INTERFACE metric 100" >> "$RC_LOCAL"
        fi
        echo "" >> "$RC_LOCAL"
        echo "exit 0" >> "$RC_LOCAL"
    else
        sed -i "/exit 0/i # 配置IPv6子网卡 $SUB_INTERFACE\nip -6 addr add $SUB_IPV6/$IPV6_PREFIX dev $MAIN_INTERFACE" "$RC_LOCAL"
        if [ -n "$IPV6_GATEWAY" ]; then
            sed -i "/exit 0/i ip -6 route add default via $IPV6_GATEWAY dev $MAIN_INTERFACE metric 100" "$RC_LOCAL"
        fi
    fi
    
    echo -e "${GREEN}配置已保存到 $RC_LOCAL${NC}"
fi

# 显示配置结果
echo
echo -e "${GREEN}===== 配置完成 =====${NC}"
echo -e "${YELLOW}IPv6网卡配置信息:${NC}"
ip -6 addr show $MAIN_INTERFACE

echo
echo -e "${YELLOW}IPv6路由表:${NC}"
ip -6 route show

# 测试IPv6连接
echo
echo -e "${YELLOW}正在测试IPv6连接...${NC}"

# 测试IPv6地址
echo -n "测试IPv6地址 ($SUB_IPV6): "
if ping6 -c 1 -W 2 -I $SUB_IPV6 ::1 &>/dev/null; then
    echo -e "${GREEN}正常${NC}"
else
    echo -e "${RED}异常${NC}"
fi

# 测试IPv6网关
if [ -n "$IPV6_GATEWAY" ]; then
    echo -n "测试IPv6网关 ($IPV6_GATEWAY): "
    if ping6 -c 1 -W 2 $IPV6_GATEWAY &>/dev/null; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${RED}异常${NC}"
    fi
fi

echo
echo -e "${GREEN}IPv6子网卡配置完成！${NC}"
echo -e "${YELLOW}配置摘要:${NC}"
echo -e "  IPv6地址: $SUB_IPV6/$IPV6_PREFIX"
[ -n "$IPV6_GATEWAY" ] && echo -e "  IPv6网关: $IPV6_GATEWAY"
echo
echo -e "${YELLOW}如需删除IPv6子网卡配置，请使用以下方法之一:${NC}"
echo -e "${YELLOW}  1. 通过主网卡名称删除: $0 -i $MAIN_INTERFACE -d${NC}"
echo -e "${YELLOW}  2. 通过IPv6地址删除: $0 -p $SUB_IPV6 -d${NC}"
